<template>
  <div class="split-dialog">
    <xyy-dialog title="拆分表单行" ref="splitDialog" width="948px">
      <div v-table class="table-box">
        <vxe-table ref="xTable" :loading="loading" highlight-current-row highlight-hover-row height="375"
          :data="tableData" @current-change="currentChangeEvent" row-id="productId">
          <vxe-table-column type="seq" title="序号" width="80" />
          <template v-for="item in splitTableColumns">
            <vxe-table-column v-if="item.visible" :key="item.field" :field="item.field" :title="item.title"
              min-width="156px" max-width="220px" :width="item.width">
              <!-- 使用插槽 -->
              <template #default="{ row }">
                <!-- 验收评定 -->
                <span v-if="item.field === 'checkAssess'">
                  <!-- 判断当前效期是否为0 -->
                  <el-select v-model="row.checkAssess" disabled v-if="row.currentMonths == 0">
                    <el-option value="2" label="不合格" />
                  </el-select>
                  <el-select v-model="row.checkAssess" @change="changeReCheck(row)" v-else>
                    <el-option v-for="item in optResult" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </span>
                <!-- 采取措施 -->
                <span v-else-if="item.field === 'measures'">
                  <!-- 判断当前效期是否为0 -->
                  <span v-if="row.currentMonths == 0">
                    <el-select v-model="row.measures" disabled>
                      <el-option value="3" label="入不合格库" />
                    </el-select>
                  </span>
                  <span v-else>
                    <el-select v-model="row.measures" :disabled="row.checkAssess != 2" v-if="row.checkAssess == 1">
                      <el-option v-for="item in measuresSelect1" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>
                    <el-select v-model="row.measures" :disabled="row.checkAssess != 2" v-else-if="row.checkAssess == 2">
                      <el-option v-for="item in measuresSelect2" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>
                    <el-select v-model="row.measures" :disabled="row.checkAssess != 2" v-else-if="row.checkAssess == 3">
                      <el-option v-for="item in measuresSelect3" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>
                  </span>
                </span>
                <!-- 复查件数 -->
                <span v-else-if="item.field === 'realPiecesNum'">
                  <el-input v-model="row.realPiecesNum" :disabled="row.realScatteredNum > 0"
                    @input="checkInputPiece(row)"></el-input>
                </span>
                <!-- 复查零散数 -->
                <span v-else-if="item.field === 'realScatteredNum'">
                  <el-input v-model="row.realScatteredNum" :disabled="row.realPiecesNum > 0"
                    @input="checkInputScattered(row)"></el-input>
                </span>
                <!-- 容器编号 -->
                <span v-else-if="item.field === 'containerCode'">
                  <el-input v-model="row.containerCode" disabled>
                    <el-button slot="append" icon="el-icon-search" @click="openCodeContainer(row)" />
                  </el-input>
                </span>
                <!-- 容器编号 -->
                <span v-else-if="item.field === 'storageClassification'">
                  <span v-if="row.storageClassification == 0">是</span>
                  <span v-else-if="row.storageClassification == 1">否</span>
                </span>
                <!-- 如果不是，则直接显示字段内容 -->
                <span v-else>{{ row[item.field] }}</span>
              </template>
            </vxe-table-column>
          </template>
        </vxe-table>
        <!-- 总数量 -->
        <span style="line-height: 40px;font-size: 16px;font-weight: 700;">总数量：{{ mediaTotal }}</span>
        <!-- 按钮组 -->
        <btn-group slot="tools" :btn-list="btnList" style="margin-top: 5px;" />
      </div>
    </xyy-dialog>
    <dialogContainer ref="dialogContainer" @on-close="closeCodeContainer"></dialogContainer>
  </div>
</template>
<script>
import { checkInputNorm } from '../../../../utils/jsjUtils'
import dialogContainer from "./dialogContainer.vue"
import { splitTableColumns } from '../config'
export default {
  name: 'splitFormRowDialog',
  components: {
    dialogContainer
  },
  data() {
    return {
      loading: false,
      tableData: [],
      oldSelectData: '',
      splitTableColumns: splitTableColumns(),
      btnList: [
        {
          label: '新增',
          type: 'primary',
          icon: 'el-icon-circle-plus-outline',
          code: 'btn:wms:saleBackInputReviewResult:newAdd',
          clickEvent: this.newAdd
        },
        {
          label: '删除',
          type: 'danger',
          icon: 'el-icon-delete',
          code: 'btn:wms:saleBackInputReviewResult:delete',
          clickEvent: this.deleteInfo
        },
        {
          label: '确认',
          type: 'primary',
          icon: 'el-icon-check',
          code: 'btn:wms:saleBackInputReviewResult:notarize',
          clickEvent: this.notarize
        },
        {
          label: '关闭',
          type: 'warning',
          icon: 'el-icon-close',
          code: 'btn:wms:saleBackInputReviewResult:close',
          clickEvent: this.closeDialog
        },
      ],
      // 验收评定
      optResult: [
        {
          value: 1,
          label: '合格'
        },
        {
          value: 2,
          label: '不合格'
        },
        // {
        //   value: 3,
        //   label: '待处理'
        // },
      ],
      // 采取措施
      measuresSelect1: [],
      measuresSelect2: [],
      measuresSelect3: [],
      selectData: '',
      oldTableData: [],
      // 复查数量和
      total: 0,
      mediaTotal: 0
    }
  },
  methods: {
    // 校验件数
    checkInputPiece(row) {
      row.realPiecesNum = checkInputNorm(row.realPiecesNum)
      this.computReviewNum(row)
      this.computeReturnNum(row)
    },
    // 校验零散数
    checkInputScattered(row) {
      row.realScatteredNum = checkInputNorm(row.realScatteredNum)
      this.computReviewNum(row)
      this.computeReturnNum(row)
    },
    // 计算复查退回数量
    computReviewNum(row) {
      row.realReturnNum = Number(row.realPiecesNum) * Number(row.packageNum) + Number(row.realScatteredNum)
      if (row.realReturnNum == 0) {
        row.containerCode = ''
      }
    },
    // 计算实际退回数量
    computeReturnNum(row) {
      row.returnNum = Number(row.realPiecesNum) * Number(row.packageNum) + Number(row.realScatteredNum)
    },
    // 打开弹框执行的回调
    open(fatherSelectData) {
      this.tableData = []
      this.mediaTotal = 0
      this.$refs.splitDialog.open()
      this.oldSelectData = JSON.parse(JSON.stringify(fatherSelectData[0]))
      this.tableData = JSON.parse(JSON.stringify(fatherSelectData))
      // 深拷贝一份初始数据
      this.oldTableData = JSON.parse(JSON.stringify(this.tableData))
      // 对总数量进行赋值
      this.tableData.forEach(item => {
        this.mediaTotal += Number(item.returnNum)
      })
      // 对采取措施列进行初始化
      this.tableData.forEach(item => {
        // 当前效期大于0，才初始化
        if (item.currentMonths > 0) {
          this.changeReCheck(item)
        }
        else {
          item.checkAssess = '2'
          item.measures = '3'
          item.unqualifiedMatters = ''
        }
      })
    },
    // 关闭弹框执行的回调
    closeDialog() {
      this.tableData = []
      this.mediaTotal = 0
      this.$refs.splitDialog.close()
    },
    // id随机值
    genID() {
      const length = 10
      return Number(
        Math.random().toString().substr(3, length) + Date.now()
      ).toString(36)
    },
    // 新增行执行的回调
    newAdd() {
      let newData = this.oldSelectData
      newData.productId = this.genID()
      newData = JSON.parse(JSON.stringify(newData))
      this.tableData.push(newData)
    },
    // 删除行执行的回调
    deleteInfo() {
      if (this.selectData == '') {
        console.log(this.selectData);
        this.$message.warning("没有选中任意行!")
        return
      }
      //一行数据不可以删除
      if (this.tableData.length === 1) {
        this.$message.warning("只剩下一行数据了,不能删除了!")
        return
      }
      //筛选删除项
      this.tableData = this.tableData.filter((item) => {
        return item.productId != this.selectData.productId
      })
      this.selectData = ''
    },
    // 确认执行的回调
    notarize() {
      this.total = 0
      this.tableData.forEach((item) => {
        this.total =
          +(item.realPiecesNum && !item.realScatteredNum
            ? item.realPiecesNum * item.packageNum
              ? item.realPiecesNum * item.packageNum
              : "0"
            : item.realScatteredNum
              ? item.realScatteredNum
              : "0") + this.total
      });
      if (this.total != this.mediaTotal) {
        this.$message.error("实际退回数量不等于复查数量")
        return
      }
      //将拆分或合并的数据传给父组件
      this.$emit("sendData", this.tableData)
      this.closeDialog()
    },
    // 切换验收评定执行的回调
    changeReCheck(row) {
      // 合格
      if (row.checkAssess == 1) {
        row.measures = '1'
        this.measuresSelect1 = [{
          value: '1',
          label: '入合格库'
        }]
        // 容器编号为初始值
        this.oldTableData.forEach(oldRow => {
          if (row.id === oldRow.id) {
            row.containerCode = oldRow.containerCode
          }
        })
      } else if (row.checkAssess == 2) {
        row.measures = '2'
        this.measuresSelect2 = [{
          value: '3',
          label: '入不合格库'
        }, {
          value: '2',
          label: '入退货区'
        }]
        // 清空容器编号
        row.containerCode = ''
      } else if (row.checkAssess == 3) {
        row.measures = '4'
        this.measuresSelect3 = [{
          value: '4',
          label: '移入待处理区，等待复查'
          ,
        }]
        // 清空容器编号
        row.containerCode = ''
      }
    },
    // 打开容器编号弹框
    openCodeContainer(row) {
      if (row.realReturnNum == 0) {
        this.numAllIsZero()
      } else {
        let busiType = 0
        const { realPiecesNum, realScatteredNum, checkAssess } = row
        if (checkAssess == 2) {
          // 不合格时，使用托盘
          busiType = 2
        } else {
          if (Number(realScatteredNum) > 0 && Number(realPiecesNum) === 0) {
            // 当只有零散数时，使用周转箱
            busiType = 1
          } else if (Number(realScatteredNum) === 0 && Number(realPiecesNum) > 0) {
            // 当只有件数时，使用托盘
            busiType = 2
          }
        }
        let name = row.reviewPerson
        this.$refs.dialogContainer.open(busiType, name)
      }
    },
    // 关闭容器编号弹框
    closeCodeContainer(codeData) {
      this.selectData.containerCode = codeData
    },
    // 单机行的回调
    currentChangeEvent(val) {
      this.selectData = val.row
    },
    // 整件数和零散数都为0的提示
    numAllIsZero() {
      this.$alert(`请填写实际件数和零散数后，才可以选择相应容器！注意：该商品是整散分开的，整件和零散不能同时填写;可以通过双击进行拆行操作，分开填写！`, '提示', {
        confirmButtonText: 'ok',
        callback: action => {
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.table-box {
  height: 400px !important;
}
</style>