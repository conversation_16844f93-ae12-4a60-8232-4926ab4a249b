<template>
  <div>
    <xyy-dialog 
      title="请输入解锁账号" 
      ref="unlockDialog" 
      width="648px"
      @on-close="closeDialog"
    >
      <el-form 
        ref="formData" 
        :model="formData" 
        label-width="120px" 
        :rules="rules" 
        class="clearfix" 
        autocomplete="off"
      >
        <el-form-item label="用户名" prop="staffNum" style="padding-bottom: 10px;">
          <el-input 
            v-model="formData.staffNum" 
            readonly 
            @focus="$event.target.removeAttribute('readonly')"
          />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input 
            v-model="formData.password" 
            show-password 
            readonly 
            @focus="$event.target.removeAttribute('readonly')"
          />
        </el-form-item>
      </el-form>
      
      <div style="display: flex;justify-content: flex-end;">
        <el-button 
          type="info" 
          @click="cancelHandler"
          v-if="getPermission('btn:wms:reviewPackage2C:cancel')"
        >
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="confirm" 
          v-if="getPermission('btn:wms:reviewPackage2C:confirm')"
        >
          确定
        </el-button>
      </div>
    </xyy-dialog>
  </div>
</template>

<script>
import { loginReview } from '@/api/buyExit/exitPreview.js';

export default {
  name: 'UnlockDialog',
  data() {
    return {
      loading: false,
      formData: {
        staffNum: "",
        password: "",
        resourceCode: "outstock:review:inReviewUnlock2C"
      },
      rules: {
        staffNum: [
          { required: true, message: '工号必填', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '密码必填', trigger: 'blur' },
        ]
      }
    };
  },
  methods: {
    // 打开弹窗
    open() {
      this.$refs.unlockDialog.open();
    },
    
    // 关闭弹框清空数据
    close() {
      this.$refs.unlockDialog.close();
    },
    
    // 按钮权限校验
    getPermission(code) {
      if (!this.$route.meta.buttonList) {
        return false;
      }
      const permissions = this.$route.meta.buttonList.map((item) => {
        return item.code;
      });
      return permissions.indexOf(code) !== -1;
    },
    
    // 确认解锁
    confirm() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.loading = true;
          const params = this.formData;
          
          loginReview(params).then(res => {
            this.loading = false;
            const { code, msg, result } = res;
            if (code === 0) {
              this.close();
              this.$refs["formData"].resetFields();
              this.$emit('on-close', result);
            } else {
              this.$message.error(msg);
            }
          }).catch(() => {
            this.loading = false;
          });
        } else {
          return false;
        }
      });
    },
    
    // 取消操作
    cancelHandler() {
      this.$refs["formData"].resetFields();
      this.close();
      this.$emit('on-close', null); // 发射取消事件
    },
    
    // 关闭弹窗
    closeDialog() {
      this.$refs["formData"].resetFields();
      this.$emit('on-close', null); // 发射取消事件
    }
  }
};
</script>

<style lang="scss" scoped>
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input__inner) {
  border-radius: 6px;
  
  &:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  margin-left: 10px;
}
</style>
