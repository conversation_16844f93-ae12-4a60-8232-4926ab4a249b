<template>
  <div class="push-stock-detail">
    <xyy-panel title="录入复查结果">
      <!-- 按钮组 -->
      <!-- <btn-group slot="tools" :btn-list="btnList" style="margin-bottom: 5px;" /> -->
      <div slot="tools" style="float: right;">
        <el-button type="warning" icon="el-icon-back" @click="goToBack"
          v-if="getPermission('btn:wms:saleBackInputReviewResult:back')">返回</el-button>
        <el-button type="primary" icon="el-icon-check" @click="whetherReview"
          v-if="isChecked && getPermission('btn:wms:saleBackInputReviewResult:confirm')">确认复查</el-button>
      </div>
      <!-- 信息展示 -->
      <el-form ref="form" :model="formData" label-width="90px" class="searchform">
        <el-row :gutter="20">
          <el-col :span="6" :md="6">
            <el-form-item label="销退复查单号:" prop="reviewCode">
              <span style="margin-left: 5px;">{{ formData.reviewCode }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6" :md="6">
            <el-form-item label="单据日期:" prop="documentTime">
              <span style="margin-left: 5px;">{{ formData.documentTime }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="5" :md="5">
            <el-form-item label="单据状态:" prop="statusDesc">
              <span style="margin-left: 5px;">{{ formData.statusDesc }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="7" :md="7">
            <el-form-item label="销退收货单号:" prop="receiveCode">
              <span style="margin-left: 5px;"> {{ formData.receiveCode }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6" :md="6">
            <el-form-item label="退货单位:" prop="returnOrganization">
              <span style="margin-left: 5px;"> {{ formData.returnOrganization }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6" :md="6">
            <el-form-item label="收货员:" prop="receiver">
              <span style="margin-left: 5px;">{{ formData.receiver }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="5" :md="5">
            <el-form-item label="验收员:" prop="checkPerson">
              <span style="margin-left: 5px;"> {{ formData.checkPerson }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="7" :md="7">
            <el-form-item label="销售订单号:" prop="orderCode">
              <span style="margin-left: 5px;"> {{ formData.orderCode }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="6" :md="6">
            <el-form-item label="验收日期:" prop="checkTime">
              <span style="margin-left: 5px;"> {{ formData.checkTime }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </xyy-panel>
    <xyy-panel title="商品明细">
      <div v-table class="table-box">
        <vxe-table ref="xTable" :loading="loading" highlight-current-row highlight-hover-row height="auto"
          :data="tableData" @cell-dblclick="openSplitDialog" @current-change="currentChangeEvent" row-id="productId">
          <vxe-table-column type="seq" title="序号" width="80" />
          <template v-for="item in detailTableColums">
            <vxe-table-column v-if="item.visible" :key="item.field" :field="item.field" :title="item.title"
              min-width="156px" max-width="220px" :width="item.width">
              <!-- 使用插槽 -->
              <template #default="{ row }">
                <!-- 验收评定 -->
                <span v-if="item.field === 'checkAssess'">
                  <!-- 判断当前效期是否为0 -->
                  <el-select v-model="row.checkAssess" disabled v-if="row.currentMonths == 0">
                    <el-option value="2" label="不合格" />
                  </el-select>
                  <el-select v-model="row.checkAssess" @change="changeReCheck(row)" v-else>
                    <el-option v-for="item in optResult" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </span>
                <!-- 采取措施 -->
                <span v-else-if="item.field === 'measures'">
                  <!-- 判断当前效期是否为0 -->
                  <span v-if="row.currentMonths == 0">
                    <el-select v-model="row.measures" disabled>
                      <el-option value="3" label="入不合格库" />
                    </el-select>
                  </span>
                  <span v-else>
                    <el-select v-model="row.measures" :disabled="row.checkAssess != 2" v-if="row.checkAssess == 1">
                      <el-option v-for="item in measuresSelect1" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>
                    <el-select v-model="row.measures" :disabled="row.checkAssess != 2" v-else-if="row.checkAssess == 2">
                      <el-option v-for="item in measuresSelect2" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>
                    <el-select v-model="row.measures" :disabled="row.checkAssess != 2" v-else-if="row.checkAssess == 3">
                      <el-option v-for="item in measuresSelect3" :key="item.value" :label="item.label"
                        :value="item.value" />
                    </el-select>
                  </span>
                </span>
                <!-- 不合格事项 -->
                <span v-else-if="item.field === 'unqualifiedMatters'">
                  <!-- 判断当前效期是否为0 -->
                  <span v-if="row.currentMonths == 0">
                    <el-select v-model="row.unqualifiedMatters" disabled>
                      <el-option value="" label="" />
                    </el-select>
                  </span>
                  <span v-else>
                    <el-select v-model="row.unqualifiedMatters" :disabled="row.checkAssess != 2"
                      v-if="row.checkAssess == 1 || row.checkAssess == 3">
                      <el-option v-for="item in unqualifiedMattersSelect1" :key="item.dictCode" :label="item.dictName"
                        :value="item.dictCode" />
                    </el-select>
                    <el-select v-model="row.unqualifiedMatters" :disabled="row.checkAssess != 2"
                      v-if="row.checkAssess == 2">
                      <el-option v-for="item in unqualifiedMattersSelect2" :key="item.dictCode" :label="item.dictName"
                        :value="item.dictCode" />
                    </el-select>
                  </span>
                </span>
                <!-- 复查件数 -->
                <span v-else-if="item.field === 'realPiecesNum'">
                  <el-input v-model="row.realPiecesNum" :disabled="row.realScatteredNum > 0"
                    @input="checkInputPiece(row)"></el-input>
                </span>
                <!-- 复查零散数 -->
                <span v-else-if="item.field === 'realScatteredNum'">
                  <el-input v-model="row.realScatteredNum" :disabled="row.realPiecesNum > 0"
                    @input="checkInputScattered(row)"></el-input>
                </span>
                <!-- 容器编号 -->
                <span v-else-if="item.field === 'containerCode'">
                  <el-input v-model="row.containerCode" disabled>
                    <el-button slot="append" icon="el-icon-search" @click="openCodeContainer(row)" />
                  </el-input>
                </span>
                <!-- 如果不是，则直接显示字段内容 -->
                <span v-else>{{ row[item.field] }}</span>
              </template>
            </vxe-table-column>
          </template>
        </vxe-table>
      </div>
    </xyy-panel>
    <split-form-row ref="splitFormRow" @sendData="receiveData"></split-form-row>
    <dialogContainer ref="dialogContainer" @on-close="closeCodeContainer"></dialogContainer>
  </div>
</template>
<script>
import utils from '@/utils'
import { reviewDetailResultSearch, reviewConfirmReview, getDisqualification } from '@/api/salesReturn/saleBackReview.js'
import dialogContainer from "./dialogContainer.vue"
import splitFormRow from './splitFormRow.vue'
import { detailTableColums } from '../config'
import { checkInputNorm } from '../../../../utils/jsjUtils'
export default {
  name: 'saleBackInputReviewResult',
  components: {
    splitFormRow,
    dialogContainer
  },
  data() {
    return {
      btnList: [
        {
          label: '返回',
          type: 'warning',
          icon: 'el-icon-back',
          code: 'btn:wms:saleBackInputReviewResult:back',
          clickEvent: this.goToBack
        },
        {
          label: '确认复查',
          type: 'primary',
          icon: 'el-icon-check',
          disabled: this.isChecked,
          code: 'btn:wms:saleBackInputReviewResult:confirm',
          clickEvent: this.whetherReview
        },
      ],
      isChecked: false,
      detailTableColums: detailTableColums(),
      loading: false,
      tableData: [],
      formData: {
        reviewCode: '',
        documentTime: '',
        status: '',
        receiveCode: '',
        returnOrganization: '',
        receiver: '',
        checkPerson: '',
        orderCode: '',
        checkTime: ''
      },
      selectData: '',
      // 复核单号
      reviewCode: '',
      optResult: [
        {
          value: 1,
          label: '合格'
        },
        {
          value: 2,
          label: '不合格'
        },
        // {
        //   value: 3,
        //   label: '待处理'
        // },
      ],
      // 采取措施
      measuresSelect1: [],
      measuresSelect2: [],
      measuresSelect3: [],
      // 不合格事项
      unqualifiedMattersSelect1: [],
      unqualifiedMattersSelect2: [],
      // 初始值
      oldTableData: []
    }
  },
  // 路由切换调用
  activated() {
    this.$nextTick(()=>{
      utils.pageActivated()
    })
    this.reviewCode = this.$route.query.reviewCode
    this.getReviewResult()
    this.getDisqualificationLists()
  },
  // 页面加载完毕调用
  mounted() {
    this.reviewCode = this.$route.query.reviewCode
    this.getReviewResult()
    this.getDisqualificationLists()
  },
  methods: {
    // 接收拆行数据
    receiveData(splitDataTable) {
      // 过滤拿到拆分后的数据
      let firstIdOfSplit = splitDataTable[0].id
      this.tableData = this.tableData.filter(item => item.id !== firstIdOfSplit)
      this.tableData.push(...splitDataTable)
      this.tableData.forEach(item => {
        // 当前效期大于0，进行初始化
        if (item.currentMonths > 0) {
          this.reviewChangeCheck(item)
        }
        else {
          item.checkAssess = '2'
          item.measures = '3'
          item.unqualifiedMatters = ''
        }
      })
    },
    // 校验件数
    checkInputPiece(row) {
      row.realPiecesNum = checkInputNorm(row.realPiecesNum)
      this.computReviewNum(row)
    },
    // 校验零散数
    checkInputScattered(row) {
      row.realScatteredNum = checkInputNorm(row.realScatteredNum)
      this.computReviewNum(row)
    },
    // 计算复查退回数量
    computReviewNum(row) {
      row.realReturnNum = Number(row.realPiecesNum) * Number(row.packageNum) + Number(row.realScatteredNum)
      if (row.realReturnNum == 0) {
        row.containerCode = ''
      }
    },
    // 返回销售退回复查单
    backReview() {
      this.$store.state.tagsView.visitedViews = this.$store.state.tagsView.visitedViews.filter(
        (item) => item.name !== "saleBackInputReviewResult"
      )
      this.$router.push({
        name: 'saleBackReviewOrder'
      })
    },
    // 获取不合格品的信息
    getDisqualificationLists() {
      getDisqualification({ dictType: "jsyy" }).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          this.unqualifiedMattersSelect2 = result || []
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 切换验收评定执行的回调
    changeReCheck(row) {
      // 合格
      if (row.checkAssess == 1) {
        this.measuresSelect1 = [{
          value: '1',
          label: '入合格库'
        }]
        row.measures = '1'
        this.unqualifiedMattersSelect1 = [{
          dictCode: '',
          dictName: ''
        }]
        row.unqualifiedMatters = ''
        // 容器编号为初始值
        this.oldTableData.forEach(oldRow => {
          if (row.id === oldRow.id) {
            row.containerCode = oldRow.containerCode
          }
        })
      } else if (row.checkAssess == '2') {
        row.measures = '2'
        this.measuresSelect2 = [{
          value: '3',
          label: '入不合格库'
        }, {
          value: '2',
          label: '入退货区'
        }]
        row.unqualifiedMatters = '采购没有要货'
        // 清空容器编号
        row.containerCode = ''
      } else if (row.checkAssess == '3') {
        row.measures = '4'
        this.measuresSelect3 = [{
          value: '4',
          label: '移入待处理区，等待复查',
        }]
        this.unqualifiedMattersSelect1 = [{
          dictCode: '',
          dictName: ''
        }]
        row.unqualifiedMatters = ''
        // 清空容器编号
        row.containerCode = ''
      }
    },
    // 复核之后的初始化
    reviewChangeCheck(row) {
      // 合格
      if (row.checkAssess == 1) {
        this.measuresSelect1 = [{
          value: '1',
          label: '入合格库'
        }]
        this.unqualifiedMattersSelect1 = [{
          dictCode: '',
          dictName: ''
        }]
        row.unqualifiedMatters = ''
        // 容器编号为初始值
        // this.oldTableData.forEach(oldRow => {
        //   if (row.id === oldRow.id) {
        //     row.containerCode = oldRow.containerCode
        //   }
        // })
      } else if (row.checkAssess == '2') {
        this.measuresSelect2 = [{
          value: '3',
          label: '入不合格库'
        }, {
          value: '2',
          label: '入退货区'
        }]
        row.unqualifiedMatters = '采购没有要货'
        // 清空容器编号
        // row.containerCode = ''
      } else if (row.checkAssess == '3') {
        this.measuresSelect3 = [{
          value: '4',
          label: '移入待处理区，等待复查',
        }]
        this.unqualifiedMattersSelect1 = [{
          dictCode: '',
          dictName: ''
        }]
        row.unqualifiedMatters = ''
        // 清空容器编号
        // row.containerCode = ''
      }
    },
    // 返回
    goToBack() {
      this.$confirm('返回将失去当前页面录入信息，是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.backReview()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    // 录入复查结果以及商品明细查询
    getReviewResult() {
      this.loading = true
      const params = {
        reviewCode: this.reviewCode
      }
      reviewDetailResultSearch(params).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          if (result.status == 1) {
            result.statusDesc = "已完成"
            this.isChecked = false
          }
          if (result.status == 0) {
            result.statusDesc = '待复查'
            this.isChecked = true
          }
          this.formData = result || {
            inboundCode: '',
            documentTime: '',
            status: '',
            checkCode: '',
            returnOrganization: '',
            receiverName: '',
            checkerName: '',
            orderCode: '',
            checkTime: '',
            statusDesc: ''
          }
          // 深拷贝一份数据
          this.oldTableData = JSON.parse(JSON.stringify(result.reviewDetailList))
          this.tableData = result.reviewDetailList || []
          this.tableData.forEach(item => {
            // 当前效期大于0，进行初始化
            if (item.currentMonths > 0) {
              this.changeReCheck(item)
            }
            else {
              item.checkAssess = '2'
              item.measures = '3'
              item.unqualifiedMatters = ''
            }
          })
        } else {
          this.formData = {
            inboundCode: '',
            documentTime: '',
            status: '',
            checkCode: '',
            returnOrganization: '',
            receiverName: '',
            checkerName: '',
            orderCode: '',
            checkTime: '',
            statusDesc: ''
          }
          this.tableData = []
          this.$message.error(msg)
        }
      })
      this.loading = false
    },
    // 打开拆分表单行弹框
    openSplitDialog(val) {
      let sameIdData = this.tableData.filter(data => data.id == val.row.id)
      this.$refs.splitFormRow.open(sameIdData)
    },
    // 确认复查提交api
    confirmReview() {
      const params = {
        reviewCode: this.formData.reviewCode,
        reviewPerson: this.formData.reviewPerson,
        list: this.tableData
      }
      reviewConfirmReview(params).then(res => {
        const { code, msg } = res
        if (code === 0) {
          this.reviewSuccess()
        } else {
          this.$message.error(msg)
        }
      })
    },
    // 单机行的回调
    currentChangeEvent(val) {
      this.selectData = val.row
    },
    // 打开容器编号弹框
    openCodeContainer(row) {
      if (row.realReturnNum == 0) {
        this.numAllIsZero()
      } else {
        let busiType = 0
        const { realPiecesNum, realScatteredNum, checkAssess } = row
        if (checkAssess == 2) {
          // 不合格时，使用托盘
          busiType = 2
        } else {
          if (Number(realScatteredNum) > 0 && Number(realPiecesNum) === 0) {
            // 当只有零散数时，使用周转箱
            busiType = 1
          } else if (Number(realScatteredNum) === 0 && Number(realPiecesNum) > 0) {
            // 当只有件数时，使用托盘
            busiType = 2
          }
        }
        let name = this.formData.reviewPerson
        this.$refs.dialogContainer.open(busiType, name)
      }
    },
    // 关闭容器编号弹框
    closeCodeContainer(codeData) {
      this.selectData.containerCode = codeData
    },
    // 确认复查进行判断
    whetherReview() {
      let numFlag = false
      let codeFlag = false
      // 判断实际件数和实际零散数是否同时为0
      for (let i = 0; i < this.tableData.length; i++) {
        let item = this.tableData[i]
        if (item.realReturnNum == 0) {
          this.$message.warning('实际件数和实际零散数不能同时为 0！')
          numFlag = false
          break
        } else {
          numFlag = true
        }
      }
      // 判断是否填写容器编号
      if (numFlag) {
        for (let item of this.tableData) {
          if (!item.containerCode) {
            this.$message.warning(`请填写容器编号!`)
            codeFlag = false
            break // 找到未填写容器编号的元素后，终止循环
          } else {
            codeFlag = true
          }
        }
      }
      // 判断数量
      if (codeFlag) {
        let flag = false
        // let mediaTotal = 0
        for( let i = 0; i < this.tableData.length; i++ ) {
          const id = this.tableData[i].id
          const targetItem = this.tableData.filter(item => item.id === id)
          // console.log(targetItem, 'targetItem'); 
          
          let mediaTotal = 0
          let realTotal = 0
          targetItem.forEach(item => {
            mediaTotal += Number(item.returnNum)
            realTotal += Number(item.realReturnNum)
          })
          if (mediaTotal !== realTotal) {
            this.differentNumber(this.tableData[i].productName, this.tableData[i].batchNum)
            flag = false
            break
          }else {
            flag = true
          }
        }
        // this.tableData.forEach(item => {
        //   mediaTotal += Number(item.returnNum)
        // })
        // const grouped = this.tableData.reduce((result, item) => {
        //   if (!result[item.id]) {
        //     result[item.id] = { realReturnNum: 0 }
        //   }
        //   result[item.id].realReturnNum += item.realReturnNum
        //   return result
        // }, {})
        // for (const id in grouped) {
        //   const { realReturnNum } = grouped[id]
        //   const targetItem = this.tableData.find(item => item.id === parseInt(id))
        //   if (targetItem && realReturnNum !== mediaTotal) {
        //     this.differentNumber(targetItem.productName, targetItem.batchNum)
        //     flag = false
        //     break
        //   } else {
        //     flag = true
        //   }
        // }
        // 判断是否近效期商品
        if (flag) {
          let notClosePeriodFlag = false
          for (let i = 0; i < this.tableData.length; i++) {
            const item = this.tableData[i]
            if (item.currentMonths > 0 && item.currentMonths <= item.ranReceveMonths && item.checkAssess == 1) {
              notClosePeriodFlag = false
              this.isClosePeriod(i + 1)
              break
            } else {
              notClosePeriodFlag = true
            }
          }
          // 如果不是则弹出提交弹框
          if (notClosePeriodFlag) {
            this.ifReview()
          }
        }
      }
    },
    // 数量错误
    differentNumber(productName, batchNum) {
      this.$alert(`名称为【${productName}】,批号为【${batchNum}】实际退回数量不等于复查数量!`,
        "提示", {
        confirmButtonText: '我知道了',
        callback: action => {
        }
      })
    },
    // 确认复查的弹框
    ifReview() {
      this.$confirm('是否确认提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.confirmReview()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    },
    // 近效期商品提交
    isClosePeriod(num) {
      this.$confirm(`第${num}行为近效期商品，是否继续提交?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.confirmReview()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '未提交验收！'
        })
      })
    },
    // 复查成功提示
    reviewSuccess() {
      this.$alert(`复查成功！`, '提示', {
        confirmButtonText: 'ok',
        callback: action => {
          this.backReview()
        }
      })
    },
    // 整件数和零散数都为0的提示
    numAllIsZero() {
      this.$alert(`请填写实际件数和零散数后，才可以选择相应容器！注意：该商品是整散分开的，整件和零散不能同时填写;可以通过双击进行拆行操作，分开填写！`, '提示', {
        confirmButtonText: 'ok',
        callback: action => {
        }
      })
    },
    getPermission(code) {
      if (!this.$route.meta.buttonList) {
        return false;
      }
      const permissions = this.$route.meta.buttonList.map((item) => {
        return item.code;
      });
      return permissions.indexOf(code) !== -1;
    }
  },
}
</script>
<style lang="scss" scoped>
.table-box {
  height: 440px !important;
}
</style>