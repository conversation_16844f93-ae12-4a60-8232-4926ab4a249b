import request from '@/utils/request'

/**
 *   复核打包-索取
 * @param {object} 
 */
 export function getPartsInReviewGoodsSalesInfo(data) {
    return request({
      url: 'outstock/web/outstock/checkPicking/getCheckPackingGoods',
      method: 'post',
      data: data
    })
}
/**
 *   复核打包-获取复核的商品
 * @param {object} 
 */
 export function listPartsInReviewGoodsView(data) {
    return request({
      url: 'outstock/web/outstock/checkPicking/getCheckPackingGoodsByMergeOrderCode',
      method: 'post',
      data: data
    })
}
/**
 *   复核打包-任务明细
 * @param {object} 
 */
 export function getPartsInReviewTaskDetail(data) {
    return request({
      url: 'outstock/web/outstock/checkPicking/queryCheckPackingDetail',
      method: 'post',
      data: data
    })
}

/**
 *   复核打包-商品明细
 */
export function getExceptionGoodsList(data) {
  return request({
     url: '/outstock/web/outstock/partsInReviewLayout/getProducts',
     method: 'post',
     params: data
  })
}

/**
 *   复核打包-保存箱码
 * @param {object} 
 */
 export function saveBoxCode(data) {
    return request({
      url: 'partsInReviewGroup/saveBoxCode',
      method: 'post',
      params: data
    })
}
/**
 *   复核打包-复核/取消复核
 * @param {object} 
 */
 export function review(data) {
    return request({
      url: 'outstock/web/outstock/checkPicking/review',
      method: 'post',
      data: data
    })
}
/**
 *   复核打包-任务列表
 * @param {object} 
 */
 export function getPartsInReviewTask(data) {
    return request({
      url: 'outstock/web/outstock/checkPicking/queryAllNoReviewTask',
      method: 'post',
      data: data
    })
}

/**
 *   复核打包-复核确认
 * @param {object} 
 */
 export function reviewConfirm(data) {
  return request({
    url: 'outstock/web/outstock/checkPicking/reviewConfirm',
    method: 'post',
    data: data
  })
}
/**
 * 获取承运商
 */
export function changeCarrier(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/calculateCarrier",
    method: "post",
    data: data,
  });
}
/**
 * 确认承运商
 */
export function changeCarrierConfirm(data) {
  return request({
    url: "/outstock/web/outstock/salesorder/changeCarrier",
    method: "post",
    data: data,
  });
}

/**
 * 根据合单号 获取拼箱列表
 * */
export function getMergeOrderList(data) {
  return request({
    url: "/outstock/web/outstock/checkPicking/getCheckPackingLcLListByMergeOrderCode",
    method: "post",
    data: data,
  });
}

/**
 * 删除拼箱 
 * */
export function deleteMergeOrder(data) {
  return request({
    url: "/outstock/web/outstock/checkPicking/deleteConsolidation",
    method: "post",
    data: data,
  });
}


/**
 * 校验耗材
 * */
export function checkParts(data) {
  return request({
    url: "/outstock/web/outstock/checkPicking/checkContainerCode",
    method: "get",
    params: data,
  });
}

/**
 * 查询临时存储的追溯码
 */
export function getTraceCode(data) {
  return request({
    url: "/outstock/web/outstock/checkPicking/getRegulatoryCodes",
    method: "get",
    params: data,
  });
}

/**
 * 暂存商品追溯码数据
 */
export function saveTraceCode(data) {
  return request({
    url: "/outstock/web/outstock/checkPicking/cacheRegulatoryCodes",
    method: "post",
    data: data,
  });
}

