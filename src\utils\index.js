import math from "./math";
import print from "./print";
import ENUM from "./enum";
import { Message } from "element-ui";
/**
 * Created by PanJiaChen on 16/11/18.
 */
const utils = {
  math,
  print,
  ENUM,
};
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string") {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), "/");
      }
    }

    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return time_str;
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (("" + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url;
  const search = url.substring(url.lastIndexOf("?") + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length;
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i);
    if (code > 0x7f && code <= 0x7ff) s++;
    else if (code > 0x7ff && code <= 0xffff) s += 2;
    if (code >= 0xdc00 && code <= 0xdfff) i--;
  }
  return s;
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return "";
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
    })
  ).join("&");
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split("?")[1]).replace(/\+/g, " ");
  if (!search) {
    return {};
  }
  const obj = {};
  const searchArr = search.split("&");
  searchArr.forEach((v) => {
    const index = v.indexOf("=");
    if (index !== -1) {
      const name = v.substring(0, index);
      const val = v.substring(index + 1, v.length);
      obj[name] = val;
    }
  });
  return obj;
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement("div");
  div.innerHTML = val;
  return div.textContent || div.innerText;
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== "object") {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property];
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      target[property] = sourceProperty;
    }
  });
  return target;
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return;
  }
  let classString = element.className;
  const nameIndex = classString.indexOf(className);
  if (nameIndex === -1) {
    classString += "" + className;
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length);
  }
  element.className = classString;
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === "start") {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    return new Date(new Date().toDateString());
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function (...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "deepClone");
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr));
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + "";
  const randomNum = parseInt((1 + Math.random()) * 65536) + "";
  return (+(randomNum + timestamp)).toString(32);
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += " " + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    ele.className = ele.className.replace(reg, " ");
  }
}

/**
 * fix 日期范围，
 * 输入为两个日期范围
 * 输出为两个日期范围-- 若输出为空，则输出为 ''
 * @param sourceDate
 * @returns {string[]}
 */
export function formatDateRange(sourceDate) {
  const resultDate = ["", ""];
  if (sourceDate) {
    resultDate[0] = sourceDate[0] || "";
    resultDate[1] = sourceDate[1] || "";
  }
  return resultDate;
}
/**
 * 对日期进行格式化，
 * @param date 要格式化的日期
 * @param format 进行格式化的模式字符串
 *     支持的模式字母有：
 *     y:年,
 *     M:年中的月份(1-12),
 *     d:月份中的天(1-31),
 *     h:小时(0-23),
 *     m:分(0-59),
 *     s:秒(0-59),
 *     S:毫秒(0-999),
 *     q:季度(1-4)
 * @return String
 */
export function dateFormat(date, format) {
  date = new Date(date);
  const map = {
    M: date.getMonth() + 1, // 月份
    d: date.getDate(), // 日
    h: date.getHours(), // 小时
    m: date.getMinutes(), // 分
    s: date.getSeconds(), // 秒
    q: Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  };

  format = format.replace(/([yMdhmsqS])+/g, function (all, t) {
    let v = map[t];
    if (v !== undefined) {
      if (all.length > 1) {
        v = "0" + v;
        v = v.substr(v.length - 2);
      }
      return v;
    } else if (t === "y") {
      return (date.getFullYear() + "").substr(4 - all.length);
    }
    return all;
  });

  return format;
}
/**
 * 表单方式下载文件
 * @param {*} url 请求路径
 * @param {*} type 请求类型，默认为get
 * @param {*} params 请求携带参数
 */
export function downFileByForm(url, type = "post", params) {
  const form = document.createElement("form");
  form.id = "form";
  form.name = "form";
  document.body.appendChild(form);
  for (const obj in params) {
    if (params.hasOwnProperty(obj)) {
      const input = document.createElement("input");
      input.tpye = "hidden";
      input.name = obj;
      input.value = params[obj];
      form.appendChild(input);
    }
  }
  form.method = type;
  form.action = url;
  form.submit();
  document.body.removeChild(form);
}
// fn 是需要节流处理的函数
// wait 是时间间隔
export function throttle(fn, wait) {
  // previous 是上一次执行 fn 的时间
  // timer 是定时器
  let previous = 0;
  let timer = null;
  // 将 throttle 处理结果当作函数返回
  return function (...args) {
    // 获取当前时间，转换成时间戳，单位毫秒
    const now = +new Date();
    // ------ 新增部分 start ------
    // 判断上次触发的时间和本次触发的时间差是否小于时间间隔
    if (now - previous < wait) {
      // 如果小于，则为本次触发操作设立一个新的定时器
      // 定时器时间结束后执行函数 fn
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        previous = now;
        fn.apply(this, args);
      }, wait);
      // ------ 新增部分 end ------
    } else {
      // 第一次执行
      // 或者时间间隔超出了设定的时间间隔，执行函数 fn
      previous = now;
      fn.apply(this, args);
    }
  };
}

/**
 * 导出excel文件
 * @param {Object} param
 * tableColumns  表格头配置
 * data  导出数据
 * fileName 导出文件名称
 */
utils.exportToExcel = function ({ tableColumns, data, fileName }) {
  const aoa = [];
  const header = [];
  tableColumns.forEach((el) => {
    header.push(el.title);
  });
  aoa.push(header);
  data.forEach((i) => {
    const tem = [];
    tableColumns.forEach((el) => {
      tem.push(i[el.property]);
    });
    aoa.push(tem);
  });
  const sheet = XLSX.utils.aoa_to_sheet(aoa);
  this.openDownloadDialog(this.sheet2blob(sheet), fileName);
};

/**
 * 通用的打开下载对话框方法
 * @param {*} url 下载地址，也可以是一个blob对象，必选
 * @param {*} fileName 保存文件名，可选
 */
utils.openDownloadDialog = function (url, fileName) {
  // if (typeof url === "object" && url instanceof Blob) {
  //   url = URL.createObjectURL(url); // 创建blob地址
  // }
  // const aLink = document.createElement("a");
  // aLink.href = url;
  // aLink.download = fileName || ""; // HTML5新增的属性，指定保存文件名，可以不要后缀，注意，file:///模式下不会生效
  // let event = null;
  // if (window.MouseEvent) event = new MouseEvent("click");
  // else {
  //   event = document.createEvent("MouseEvents");
  //   event.initMouseEvent(
  //     "click",
  //     true,
  //     false,
  //     window,
  //     0,
  //     0,
  //     0,
  //     0,
  //     0,
  //     false,
  //     false,
  //     false,
  //     false,
  //     0,
  //     null
  //   );
  // }
  // aLink.dispatchEvent(event);
  /***上面代码无法指定文件名，改为下面代码*** */
  fetch(url)
  .then(response => response.blob())
  .then(blob => {
    const url = URL.createObjectURL(blob);
    const aLink = document.createElement("a");
    aLink.href = url;
    aLink.download = fileName || ""; // 设置下载文件名
    aLink.click();
    URL.revokeObjectURL(url); // 释放URL对象
  })
  .catch(err => console.error("Download failed:", err));
};
// post 请求，使用 formData 方式提交
export function formData(options) {
  const params = new FormData();
  Object.keys(options).forEach((key) => {
    params.append(key, options[key]);
  });
  return params;
}
// 本地缓存方法封装
export const store = {
  name: "store",
  set: function (key, value) {
    window.localStorage.setItem(key, JSON.stringify(value));
  },
  get: function (key) {
    return JSON.parse(window.localStorage.getItem(key));
  },
  remove: function (key) {
    window.localStorage.removeItem(key);
  },
  clear: function () {
    window.localStorage.clear();
  },
};
utils.pushUnique = function (arr, item) {
  if (arr.indexOf(item) === -1) {
    arr.push(item);
  }
  return arr;
};
//根据键名排序
utils.arraySort = function (arr, key) {
  return arr.sort((a, b) => {
    if (a[key] < b[key]) {
      return -1;
    }
    if (a[key] > b[key]) {
      return 1;
    }
    return 0;
  });
};

//el-button 按钮权限封装
//v-if(apiGetPermission('code') && ...) method: {apiGetPermission}
export function getPermission(code) {
  if (!this.$route.meta.buttonList) {
    return false;
  }
  const permissions = this.$route.meta.buttonList.map((item) => {
    return item.code;
  });
  return permissions.indexOf(code) !== -1;
}

/**
 * 根据数据计算表格列宽
 * @param {Array} columns 表格头配置，必填
 * @param {Array} tableData 表格数据，必填
 * @param {Number} columnSize 每个字符宽度倍率，默认24，可选
 * @param {Number} maxSize 最大宽度，默认300px,可选
 * @param {Number} slotSize slot列宽度，默认165px，可选
 */
export function getMinWidth(columns, tableData, columnSize, maxSize, slotSize) {
  if (columnSize === undefined || columnSize === null || columnSize === "") {
    columnSize = 24;
  }
  if (maxSize === undefined || maxSize === null || maxSize === "") {
    maxSize = 300;
  }
  if (slotSize === undefined || slotSize === null || slotSize === "") {
    slotSize = 165;
  }
  if (tableData.length > 0) {
    columns.forEach((column) => {
      let sumWidth = 0;
      let maxLength = tableData.reduce((max, row) => {
        return Math.max(max, String(row[column.field]).length);
      }, column.title.length);
      // console.log(maxLength, 'maxLength', column.title);
      const datePattern = /\d{4}-\d{2}-\d{2}/;
      const dateTimePattern = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
      const numberPattern = /^\d+$/;
      const goodsCodePattern = /^[a-zA-Z]\d+$/;
      const codePattern = /^[a-zA-Z]{3}\d+$/;
      maxLength = Math.max(maxLength, column.title.length);
      if (maxLength >= 8) {
        column.width = parseInt(maxLength * (columnSize / 2));
        sumWidth = parseInt(maxLength * (columnSize / 2));
        if (
          datePattern.test(tableData[0][column.field]) ||
          dateTimePattern.test(tableData[0][column.field])
        ) {
          column.width = parseInt(maxLength * (columnSize / 2.5));
          sumWidth = parseInt(maxLength * (columnSize / 2.5));
        }
      } else {
        column.width = parseInt(maxLength * columnSize);
        sumWidth = parseInt(maxLength * columnSize);
      }
      if (maxLength != column.title.length && maxLength / 2 > column.title.length) {
        if (
          goodsCodePattern.test(tableData[0][column.field]) ||
          codePattern.test(tableData[0][column.field]) ||
          numberPattern.test(tableData[0][column.field])
        ) {
          column.width = parseInt(maxLength * (columnSize / 1.9));
          sumWidth = parseInt(maxLength * (columnSize / 1.9));
        }
      }
      if (column.slot) {
        column.width = slotSize;
        sumWidth = slotSize;
      }
      if (column.slot == "button") {
        column.width = parseInt(slotSize / 2);
        sumWidth = parseInt(slotSize / 2);
      }
      if (sumWidth > maxSize) {
        column.width = maxSize;
      }
    });
    // console.log(this.tableColumns, 'tableColumns');
    return columns;
  } else {
    columns.forEach((column) => {
      column.width = column.title.length * columnSize;
    });
    // console.log(this.tableColumns, 'tableColumns');
    return columns;
  }
}
utils.pageActivated = function pageActivated() {
  const el = document.getElementsByClassName('vxe-table--body-wrapper')[0];
  const pg = document.getElementsByClassName('vxe-pager')[0];
  const tb = document.getElementsByClassName('vxe-table')[0];
  const tx = document.getElementsByClassName('table-box')[0];
  const th = document.getElementsByClassName('vxe-header--column')[0];

  if (!el || !pg || !tb || !th) {
    console.error('无法找到必要的 DOM 元素');
    return;
  }

  console.log("el", el);

  const offsetTop = tx ? tx.offsetTop : tb.offsetTop;

  el.style.height = `${pg.offsetTop - offsetTop - 20 - th.clientHeight}px`;
  console.log("el.style.height", el.style.height, pg.offsetTop, offsetTop, th.clientHeight);
}

/**
 * 请求导出文件
 * @param {String} url 请求地址，必填
 * @param {String} type 请求类型,默认post，可选
 * @param {Object} params 请求参数，可选
 */
export async function downloadFileByRequest(url, type = 'post', params) {
  try {
    const axios = require('axios');
    const response = await axios[type](`/api${url}`, params ? params : {}, {
      responseType: 'blob'
    })

    const { data, headers } = response;
    console.log(response, 'response');
    

    // 确保 `content-disposition` 头部存在
    const contentDisposition = headers['content-disposition'] || '';
    let fileName = contentDisposition.split('filename=')[1];

    // 确保文件名存在并去掉前后的下划线
    if (fileName) {
      fileName = fileName.replace(/^_+|_+$/g, '').trim();
      fileName = decodeURIComponent(fileName.replace(/['"]/g, ''));
    } else {
      fileName = 'downloaded_file'; // 如果未能解析出文件名，则使用默认文件名
    }

    // 创建 Blob 对象
    const blob = new Blob([data], { type: headers['content-type'] });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link); // 防止在 Firefox 中出现问题
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    Message({
      message: '文件下载成功',
      type: 'success',
    })
  } catch (err) {
    console.error('文件下载失败:', err);
    Message({
      message: '文件下载失败',
      type: 'error',
    })
  };
}
export default utils;
