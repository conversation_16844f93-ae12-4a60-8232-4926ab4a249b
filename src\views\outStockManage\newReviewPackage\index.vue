<template>
    <div class="new-review-package-container" v-loading="mainLoading">
        <!-- 顶部操作区 -->
        <div class="header-section">
            <!-- 左区块 (40%) - 对应左侧商品信息区的比例 -->
            <div class="header-left-block">
                <div class="input-group">
                    <label>销售单号</label>
                    <el-input ref="erpOrderCode" v-model="formData.erpOrderCode" placeholder="请输入销售单号或扫描条码，或按回车"
                        class="sales-input" :disabled="isSearchErp" onfocus="this.select()"
                        @keyup.enter.native="handleSalesOrderSearch" />
                    <el-button type="primary" @click="handleViewTasks" class="shortkey-button">
                        <span class="button-content">
                            <span class="button-label">查看任务</span>
                            <span class="button-shortkey">F4</span>
                        </span>
                    </el-button>
                    <el-button type="success" @click="handleRefresh" class="shortkey-button">
                        <span class="button-content">
                            <span class="button-label">刷新</span>
                            <span class="button-shortkey">F2</span>
                        </span>
                    </el-button>
                </div>
                <div class="order-info">
                    <span class="info-item">随货同行单数量：<span class="count">{{ orderStats.totalCount }}</span></span>
                    <span class="info-item">未扫：<span class="count red">{{ unscannedCount }}</span></span>
                </div>
                <div class="input-group material-code-group">
                    <label>耗材码</label>
                    <el-input ref="materialCode" v-model="formData.materialCode" placeholder="请扫描耗材码"
                        onfocus="this.select()" @keyup.enter.native="handleMaterialCodeScan" />
                </div>
            </div>

            <!-- 右区块 (60%) - 对应右侧区域的比例 -->
            <div class="header-right-block">
                <!-- 第一部分：快递类型和订单类型信息 -->
                <div class="type-info-section">
                    <div class="type-info">
                        <div class="express-type">
                            <span class="label">快递类型：</span>
                            <span class="value">
                                <span style="color: #409eff;">{{ orderInfo.carrierName || '-' }}</span>
                                <span style="color: red;">{{ getAttributes() }}</span>
                            </span>
                        </div>
                        <div class="order-type">
                            <span class="label">订单类型：</span>
                            <span class="value">
                                <span style="color: red;" v-if="orderInfo.isMajorClients">大客户</span>
                                <span style="color: #409eff;" v-else>-</span>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 第二部分：耗材录入数据表格 -->
                <div class="materials-section">
                    <div class="header-materials-area">
                        <div class="materials-table-container">
                            <vxe-table ref="headerMaterialsTable" :data="materialsData"
                                :height="headerMaterialsTableHeight" :max-height="headerMaterialsTableHeight" stripe
                                border highlight-hover-row show-overflow :scroll-y="{ enabled: false }"
                                :scroll-x="{ enabled: false }" :auto-resize="false">
                                <vxe-table-column field="materialCode" title="耗材编码" min-width="80" />
                                <vxe-table-column field="materialName" title="耗材名称" min-width="80" />
                                <vxe-table-column title="操作" width="80">
                                    <template #default="{ row, rowIndex }">
                                        <el-button type="text" size="small"
                                            @click="deleteMaterial(row, rowIndex)">删除</el-button>
                                    </template>
                                </vxe-table-column>
                            </vxe-table>
                        </div>
                    </div>
                </div>

                <!-- 第三部分：复核排名信息控件（占位） -->
                <div class="ranking-info-section">
                    <div class="ranking-placeholder">
                        <div class="placeholder-content">
                            <!-- <span class="placeholder-text">复核排名</span>
                            <span class="placeholder-subtitle">功能开发中</span> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 左侧商品信息区 -->
            <div class="left-section">


                <!-- 商品条码和数量输入区域 -->
                <div class="scan-input-area">
                    <div class="input-row">
                        <div class="barcode-input-group">
                            <label>商品条码</label>
                            <el-input ref="barCode" v-model="formData.barCode" placeholder="请扫描商品条码"
                                onfocus="this.select()" @keyup.enter.native="handleBarCodeScan" />
                        </div>
                        <div class="quantity-input-group">
                            <label>数量</label>
                            <el-input v-model="formData.quantity" placeholder="请输入数量" />
                        </div>
                    </div>
                </div>

                <div class="product-info">
                    <div class="info-row">
                        <span class="label">商品名称：</span>
                        <span class="value">{{ currentProduct.productName || '-' }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">商品编码：</span>
                        <span class="value">{{ currentProduct.productCode || '-' }}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">包装单位：</span>
                        <span class="value">{{ currentProduct.packingUnit || '-' }}</span>
                    </div>

                    <!-- 存储属性扩展信息 -->
                    <div class="warning-class" v-if="currentProduct.storageAttributesExt">
                        {{ currentProduct.storageAttributesExt }}
                    </div>

                    <!-- 赠品信息 -->
                    <div class="gift-class" v-if="currentProduct.productGiftsName">
                        {{ currentProduct.productGiftsName }}：{{ currentProduct.productGiftsNumber }} {{
                            currentProduct.giftsPackingUnit }}
                    </div>
                </div>

                <div class="product-image-container">
                    <div class="image-placeholder">
                        <img v-if="currentProduct.image" :src="currentProduct.image" alt="商品图片" />
                        <div v-else class="no-image">商品图片</div>
                    </div>
                </div>
            </div>

            <!-- 右侧区域 -->
            <div class="right-area">
                <!-- 右上：商品列表区 -->
                <div class="right-section">
                    <div class="product-table-container">
                        <vxe-table ref="productTable" :data="productList" :height="productTableHeight"
                            :max-height="productTableHeight" stripe border highlight-hover-row highlight-current-row
                            :cell-class-name="productCellClassName" show-overflow :scroll-y="{ enabled: true, gt: 5 }"
                            :scroll-x="{ enabled: true }" @cell-click="handleProductRowClick"
                            @cell-dblclick="handleProductClick">
                            <vxe-table-column field="barCode" title="商品条码" min-width="100" />
                            <vxe-table-column field="productName" title="商品名称" min-width="100" />
                            <vxe-table-column field="batchNumber" title="商品批号" min-width="100" />
                            <vxe-table-column field="productCode" title="商品编码(双击复核)" min-width="100">
                                <template #header>
                                    <div slot="content">
                                        <div>商品编码</div>
                                        <div>(双击复核)</div>
                                    </div>
                                </template>
                            </vxe-table-column>
                            <vxe-table-column field="packedCount" title="已装数" min-width="70" />
                            <vxe-table-column field="remainingQuantity" title="未装数" min-width="70"
                                :formatter="remainingQuantityFormatter" />
                            <vxe-table-column field="reviewNumber" title="装箱总数" min-width="90" />
                        </vxe-table>
                    </div>
                </div>

                <!-- 右下：操作和统计区 -->
                <div class="bottom-section">
                    <!-- 上方区域：操作按钮和统计信息 -->
                    <div class="top-area">
                        <!-- 左侧区域：操作按钮和统计信息 -->
                        <div class="left-area">
                            <!-- 操作按钮区域 -->
                            <div class="operation-area">
                                <div class="action-buttons">
                                    <el-button type="primary" @click="handlePackageConfirm" class="shortkey-button">
                                        <span class="button-content">
                                            <span class="button-label">复核确认</span>
                                            <span class="button-shortkey">F7</span>
                                        </span>
                                    </el-button>
                                    <el-button type="warning" @click="handleExceptionSubmit" class="shortkey-button">
                                        <span class="button-content">
                                            <span class="button-label">异常提交</span>
                                            <span class="button-shortkey">F1</span>
                                        </span>
                                    </el-button>
                                    <el-button type="info" @click="handleCarrierChange" class="shortkey-button">
                                        <span class="button-content">
                                            <span class="button-label">更换承运商</span>
                                            <span class="button-shortkey">F10</span>
                                        </span>
                                    </el-button>
                                </div>
                            </div>

                            <!-- 统计信息区域 -->
                            <div class="stats-area">
                                <div class="stats-row">
                                    <div class="stat-item">
                                        <span class="label">已装箱商品总数：</span>
                                        <span class="value blue">{{ packageStats.packedTotal }}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="label">未装箱商品总数：</span>
                                        <span class="value red">{{ packageStats.unpackedTotal }}</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="label">商品总数：</span>
                                        <span class="value red">{{ packageStats.totalProducts }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>

                    <!-- 下方区域：已扫商品信息表格 -->
                    <div class="bottom-area">
                        <div class="scanned-products-area">
                            <!-- <div class="scanned-products-title">已扫商品信息</div> -->
                            <div class="scanned-products-table-container">
                                <vxe-table ref="scannedProductsTable" :data="scannedProductsData"
                                    :height="scannedProductsTableHeight" :max-height="scannedProductsTableHeight" stripe
                                    border highlight-hover-row show-overflow :scroll-y="{ enabled: true, gt: 5 }"
                                    :scroll-x="{ enabled: true }" @cell-click="handleScannedProductRowClick"
                                    @cell-dblclick="handleScannedProductDblClick"
                                    :cell-class-name="scannedProductCellClassName">
                                    <!-- <vxe-table-column type="seq" title="序号" width="60" /> -->
                                    <vxe-table-column field="barCode" title="商品条码" min-width="100" />
                                    <vxe-table-column field="productName" title="商品名称" min-width="100" />
                                    <vxe-table-column field="packedCount" title="已装箱数量" min-width="110" />
                                    <vxe-table-column field="unpackedCount" title="未装箱数量" min-width="110"
                                        :formatter="unpackedCountFormatter" />
                                    <vxe-table-column field="batchNumber" title="商品批号" min-width="100" />
                                    <vxe-table-column field="productCode" title="商品编码" min-width="100" />
                                </vxe-table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- 子组件弹窗 -->
        <view-tasks-dialog ref="viewTasksDialog" @on-close="handleDialogClose" @on-choose-row="handleChooseRow" />
        <exception-submit-dialog ref="exceptionSubmitDialog" @on-close="handleDialogClose" />
        <tracing-code-dialog ref="tracingCodeDialog" @on-close="handleDialogClose"
            @regulatoryCodeScanBack="handleTracingCodeComplete" />

        <!-- 承运商更换确认弹窗 -->
        <changeCYSAlertNew ref="dialogChangeCYSAlert" />

        <!-- 数量差异处理弹窗 -->
        <quantity-difference-dialog ref="quantityDifferenceDialog" @on-difference-submitted="handleDifferenceSubmitted"
            @on-validation-failed="handleValidationFailed" @on-cancelled="handleDifferenceCancelled" />
    </div>
</template>

<script>
import { doTTS } from "@/utils/tts.js";
import ViewTasksDialog from "./components/viewTasksDialog.vue";
import ExceptionSubmitDialog from "./components/exceptionSubmitDialog.vue";
import TracingCodeDialog from "./components/tracingCodeDialog.vue";
import changeCYSAlertNew from "../reviewPackage/components/changeCYSAlertNew.vue";
import QuantityDifferenceDialog from "./components/quantityDifferenceDialog.vue";

// 引入现有模块的API接口
import {
    getPartsInReviewGoodsSalesInfo,
    listPartsInReviewGoodsView,
    review,
    reviewConfirm,
    checkParts,
    changeCarrier
} from "@/api/outstock/fhdb";

import { submitException } from "@/api/outstock/distribution";
import { printNew } from "@/utils/print";

export default {
    name: "NewReviewPackage",
    components: {
        ViewTasksDialog,
        ExceptionSubmitDialog,
        TracingCodeDialog,
        changeCYSAlertNew,
        QuantityDifferenceDialog
    },
    data() {
        return {
            mainLoading: false,
            hasDialog: false, // 弹窗状态控制
            checkCancelOrder: 1, // 取消订单检查标志，默认值为1表示需要检查

            // 输入框禁用状态控制
            isSearchErp: false, // 销售单号是否已搜索（控制销售单号输入框禁用）
            // addBoxCodeOpen: false, // 耗材码输入框是否开启

            // 防抖定时器
            resizeTimer: null, // 窗口resize事件防抖定时器

            // 表单数据
            formData: {
                erpOrderCode: "", // 销售单号
                materialCode: "", // 耗材码
                barCode: "", // 商品条码
                quantity: "", // 数量
                orderCode: "", // 出库单号
                allocationCode: "", // 分配单号
                mergeOrderCode: "", // 合单单号
                consolidationCode: "", // 合箱单号
                supervisoryCodeAcquisition: "", // 监管码获取
                orderNumberCancelled: "", // 订单取消数量
                inreviewProductNumber: "" // 复核中商品数量
            },

            // 当前选中商品信息
            currentProduct: {
                productCode: "",
                productName: "",
                productNumber: "",
                packingUnit: "",
                image: "",
                // 新增存储属性和赠品相关字段
                storageAttributesExt: "",      // 存储属性扩展
                productGiftsName: "",          // 赠品名称
                productGiftsNumber: "",        // 赠品数量
                giftsPackingUnit: ""           // 赠品单位
            },

            // 商品列表数据
            productList: [],

            // 统计数据
            orderStats: {
                totalCount: 0,    // 随货同行单总数量
                checkedCount: 0   // 随货同行单已检数量
            },

            packageStats: {
                packedTotal: 0,
                unpackedTotal: 0,
                totalProducts: 0
            },

            // 订单信息
            orderInfo: {
                expressType: "", // 快递类型
                orderType: "", // 订单类型
                carrierName: "", // 承运商名称
                attributes: "", // 特殊属性
                isMajorClients: false // 是否大客户
            },

            detailStats: [],

            // 耗材数据
            materialsData: [],

            // 已扫商品信息数据
            scannedProductsData: [],

            // 快捷键配置
            hotkeys: "F1,F2,F4,F7,F10"
        };
    },

    computed: {
        // 计算未扫的随货同行单数量
        unscannedCount() {
            if (!this.orderStats) return 0;
            return (this.orderStats.totalCount || 0) - (this.orderStats.checkedCount || 0);
        },
        // 商品表格高度
        productTableHeight() {
            if (window.innerWidth <= 768) {
                return '250px';
            } else if (window.innerWidth <= 1200) {
                return '300px';
            }
            return '400px';
        },

        // 耗材表格高度（固定高度，确保不会撑开容器）
        materialsTableHeight() {
            if (window.innerWidth <= 768) {
                return '80px'; // 小屏幕使用更小的固定高度
            }
            return '100px'; // 桌面端进一步减小高度，节省垂直空间
        },

        // 顶部耗材表格高度（专门用于header区域的耗材表格）
        headerMaterialsTableHeight() {
            if (window.innerWidth <= 480) {
                return 80; // 极小屏幕：80px
            } else if (window.innerWidth <= 768) {
                return 100; // 小屏幕：80px
            } else if (window.innerWidth <= 1200) {
                return 120; // 中等屏幕：120px
            }
            return 140; // 大屏幕：140px
        },

        // 已扫商品表格高度
        scannedProductsTableHeight() {
            if (window.innerWidth <= 768) {
                return '180px';
            } else if (window.innerWidth <= 1200) {
                return '250px';
            }
            return '300px';
        }
    },

    created() {
        this.initHotkeys();
    },

    activated() {
        // 页面激活时聚焦到销售单号输入框
        this.$refs.erpOrderCode.focus();
    },

    mounted() {
        // 监听窗口大小变化，重新计算表格高度
        window.addEventListener('resize', this.handleResize);

        // 初始化时调整右区块布局和表头固定
        this.$nextTick(() => {
            this.adjustRightBlockLayout();
            this.ensureTableHeaderFixed();
        });
    },

    beforeDestroy() {
        this.$hotkeys.unbind(this.hotkeys);
        // 移除窗口大小变化监听器
        window.removeEventListener('resize', this.handleResize);
        // 清理防抖定时器
        if (this.resizeTimer) {
            clearTimeout(this.resizeTimer);
            this.resizeTimer = null;
        }
    },

    methods: {
        // 任务弹窗双击进行销售单号扫描
        handleChooseRow(row) {
            this.$set(this.formData, "erpOrderCode", row.erpOrderCode);
            this.handleSalesOrderSearch();
        },
        // 初始化快捷键
        initHotkeys() {
            this.$hotkeys(this.hotkeys, (event, handler) => {
                if (this.hasDialog) {
                    return false;
                }
                event.preventDefault();
                switch (handler.key) {
                    case "F1":
                        this.handleExceptionSubmit();
                        break;
                    case "F2":
                        this.handleRefresh();
                        break;
                    case "F4":
                        this.handleViewTasks();
                        break;
                    case "F7":
                        this.handlePackageConfirm();
                        break;
                    case "F10":
                        this.handleCarrierChange();
                        break;
                }
            });
        },

        // 销售单号查询
        handleSalesOrderSearch() {
            if (!this.formData.erpOrderCode.trim()) {
                this.$message.warning("请输入销售单号");
                return;
            }

            this.mainLoading = true;
            getPartsInReviewGoodsSalesInfo({
                erpOrderCode: this.formData.erpOrderCode
            }).then(res => {
                this.mainLoading = false;
                const { code, msg, result } = res;
                if (code === 0) {
                    this.handleSalesOrderSuccess(result);
                } else {
                    this.$message.error(msg);
                    this.clearPageData();
                    this.$refs.erpOrderCode.focus();
                }
            }).catch(() => {
                this.mainLoading = false;
                this.clearPageData();
                this.$refs.erpOrderCode.focus();
            });
        },

        // 销售单号查询成功处理
        handleSalesOrderSuccess(result) {
            this.formData.orderCode = result.orderCode;
            this.formData.allocationCode = result.allocationCode;
            this.formData.mergeOrderCode = result.mergeOrderCode;

            // 检查是否有整件任务（参考原页面1148-1149行）
            if (result.wholeFlag === true) {
                this.$alert('该复核任务有整件任务', '提示', {
                    confirmButtonText: '确定',
                    type: 'warning',
                }).then(() => {
                    // 继续处理后续逻辑
                    this.processSalesOrderData(result);
                });
            } else {
                // 没有整件任务，直接处理
                this.processSalesOrderData(result);
            }
        },

        // 处理销售单号数据（提取公共逻辑）
        processSalesOrderData(result) {
            // 赋值所有必要的字段（参考原页面1153-1179行）
            this.formData.consolidationCode = result.consolidationCode;
            this.formData.supervisoryCodeAcquisition = result.supervisoryCodeAcquisition;
            this.formData.orderNumberCancelled = result.orderNumberCancelled;
            this.formData.inreviewProductNumber = result.inreviewProductNumber;

            // 清空已扫商品数据，准备进行数据分流
            this.scannedProductsData = [];

            // 更新统计数据
            this.updateStats(result);

            // 加载商品列表（会触发数据分流）
            this.loadProductList(false);

            // 设置销售单号输入框为禁用状态
            this.isSearchErp = true;
        },

        // 加载商品列表
        loadProductList(autoSelectFirst = true) {
            return listPartsInReviewGoodsView({
                orderCode: this.formData.orderCode,
                allocationCode: this.formData.allocationCode,
                mergeOrderCode: this.formData.mergeOrderCode
            }).then(res => {
                const { code, msg, result } = res;
                if (code === 0) {
                    const allProducts = result || [];

                    // 数据分流：根据复核状态分别处理已复核和未复核商品
                    this.processProductDataFlow(allProducts);

                    // 可选的默认选择第一个商品（优先选择未复核商品，如果没有则选择已复核商品）
                    if (autoSelectFirst) {
                        if (this.productList.length > 0) {
                            this.selectProduct(this.productList[0]);
                        } else if (this.scannedProductsData.length > 0) {
                            this.selectProductForDisplay(this.scannedProductsData[0]);
                        }
                    }

                    // 商品列表加载完成后的光标跳转逻辑（参考原页面1095-1099行）
                    let reviewNum = 0;
                    allProducts.forEach(item => {
                        if (item.reviewStatus === 2) {
                            reviewNum++;
                        }
                    });

                    // 检查是否所有商品都已复核且统计数据匹配
                    const allReviewed = allProducts.length === reviewNum;
                    const statsMatch = this.packageStats.packedTotal === this.packageStats.totalProducts;

                    if (allReviewed && statsMatch) {
                        // 如果所有商品已复核且统计匹配，跳转到耗材码输入框
                        this.$refs.materialCode.select();
                    } else {
                        // 否则跳转到商品条码输入框
                        this.$refs.barCode.select();
                    }
                } else {
                    this.$message.error(msg);
                }
            });
        },

        // 选择商品
        selectProduct(product) {
            this.currentProduct = {
                productCode: product.productCode,
                productName: product.productName,
                productNumber: product.productNumber,
                packingUnit: product.packingUnit,
                barCode: product.barCode, // 添加条码字段用于行样式判断
                image: product.productPic && product.productPic[0] ? product.productPic[0] : "",
                // 添加存储属性和赠品相关字段
                storageAttributesExt: product.storageAttributesExt || "",
                productGiftsName: product.productGiftsName || "",
                productGiftsNumber: product.productGiftsNumber || "",
                giftsPackingUnit: product.giftsPackingUnit || ""
            };

            // 更新数量输入框，显示同品不同批商品的复核数量汇总
            this.formData.quantity = this.calculateSameProductTotalQuantity(product);
        },

        // 选择商品用于详情展示（不触发复核流程）
        selectProductForDisplay(product) {
            this.currentProduct = {
                productCode: product.productCode,
                productName: product.productName,
                packingUnit: product.packingUnit,
                barCode: product.barCode, // 添加条码字段用于行样式判断
                image: product.productPic && product.productPic[0] ? product.productPic[0] : "",
                // 添加存储属性和赠品相关字段
                storageAttributesExt: product.storageAttributesExt || "",
                productGiftsName: product.productGiftsName || "",
                productGiftsNumber: product.productGiftsNumber || "",
                giftsPackingUnit: product.giftsPackingUnit || ""
            };

            // 更新数量输入框，根据商品状态显示对应数量
            // 对于已复核商品：显示同品不同批已装数量汇总
            // 对于未复核商品：显示同品不同批复核数量汇总
            let displayQuantity = '';
            if (product.reviewStatus === 2) {
                // 已复核商品，显示同品不同批已装数量汇总
                displayQuantity = this.calculateSameProductPackedQuantity(product);
            } else {
                // 未复核商品，显示同品不同批复核数量汇总
                displayQuantity = this.calculateSameProductTotalQuantity(product);
            }
            this.formData.quantity = displayQuantity;
        },

        // 选择刚复核的商品（复核成功后调用）
        selectReviewedProduct(reviewedProduct) {
            // 优先在已扫商品列表中查找同品同批的商品
            const scannedProduct = this.scannedProductsData.find(item =>
                item.productCode === reviewedProduct.productCode &&
                item.batchNumber === reviewedProduct.batchNumber
            );

            if (scannedProduct) {
                // 如果在已扫商品列表中找到，显示已扫商品的信息
                this.selectProductForDisplay(scannedProduct);
            } else {
                // 如果没有找到，显示复核时的商品信息
                this.currentProduct = {
                    productCode: reviewedProduct.productCode,
                    productName: reviewedProduct.productName,
                    packingUnit: reviewedProduct.packingUnit,
                    barCode: reviewedProduct.barCode,
                    image: reviewedProduct.productPic && reviewedProduct.productPic[0] ? reviewedProduct.productPic[0] : "",
                    // 添加存储属性和赠品相关字段
                    storageAttributesExt: reviewedProduct.storageAttributesExt || "",
                    productGiftsName: reviewedProduct.productGiftsName || "",
                    productGiftsNumber: reviewedProduct.productGiftsNumber || "",
                    giftsPackingUnit: reviewedProduct.giftsPackingUnit || ""
                };

                // 数量输入框显示同品不同批商品的已装数量汇总
                this.formData.quantity = this.calculateSameProductPackedQuantity(reviewedProduct);
            }
        },

        // 计算同品不同批商品的复核数量汇总（参考原页面逻辑）
        calculateSameProductTotalQuantity(product) {
            if (!product || !product.productCode) {
                return '';
            }

            let totalQuantity = 0;

            // 在未复核商品列表中查找所有相同商品编码的商品
            this.productList.forEach(item => {
                if (item.productCode === product.productCode) {
                    totalQuantity += parseInt(item.reviewNumber) || 0;
                }
            });

            return totalQuantity.toString();
        },

        // 计算同品不同批商品的已装数量汇总
        calculateSameProductPackedQuantity(product) {
            if (!product || !product.productCode) {
                return '';
            }

            let totalPackedQuantity = 0;

            // 在已扫商品列表中查找所有相同商品编码的商品
            this.scannedProductsData.forEach(item => {
                if (item.productCode === product.productCode) {
                    totalPackedQuantity += parseInt(item.packedCount) || 0;
                }
            });

            return totalPackedQuantity.toString();
        },

        // 将商品行移动到表格第一行并高亮显示
        moveProductToTop(product) {
            // 找到匹配的商品在列表中的索引
            // 修复：添加批号比较，确保移动正确的批号商品
            const productIndex = this.productList.findIndex(item =>
                item.productCode === product.productCode &&
                item.productNumber === product.productNumber &&
                item.batchNumber === product.batchNumber
            );

            if (productIndex > 0) {
                // 如果商品不在第一行，将其移动到第一行
                const productToMove = this.productList.splice(productIndex, 1)[0];
                this.productList.unshift(productToMove);
            }

            // 强制更新表格以触发重新渲染和行样式更新
            this.$nextTick(() => {
                this.$forceUpdate();
            });
        },

        // 处理商品数据分流：根据复核状态分别处理已复核和未复核商品
        // 修复说明：此方法完全基于接口返回的reviewStatus来决定商品显示位置，
        // 确保同品同批商品的复核状态与后端接口保持一致
        processProductDataFlow(allProducts) {
            // 分离已复核和未复核商品
            const reviewedProducts = []; // 已复核商品
            const unreviewedProducts = []; // 未复核商品

            allProducts.forEach(product => {
                if (product.reviewStatus === 2) {
                    // 已复核商品 - 基于接口状态的已复核商品
                    const processedProduct = {
                        ...product,
                        packedCount: product.reviewNumber || 0,  // 取复核数量
                        unpackedCount: 0                         // 默认为0
                    };
                    reviewedProducts.push(processedProduct);
                } else {
                    // 未复核商品 - 设置初始装箱状态
                    const processedProduct = {
                        ...product,
                        packedCount: 0,                          // 默认为0
                        unpackedCount: product.reviewNumber || 0 // 取复核数量
                    };
                    unreviewedProducts.push(processedProduct);
                }
            });
            this.scannedProductsData = [];
            // 将已复核商品添加到已扫商品信息表格
            reviewedProducts.forEach(product => {
                this.moveProductToScanned(product);
            });

            // 将未复核商品保留在商品列表区表格
            this.productList = unreviewedProducts;

            // console.log(`数据分流完成: 已复核商品 ${reviewedProducts.length} 个，未复核商品 ${unreviewedProducts.length} 个`);
        },

        // 检查是否存在多编码商品（参考原页面731-762行）
        checkForDuplicateBarCodes() {
            const barCodeMap = new Map();
            const duplicateBarCodes = new Set();

            this.productList.forEach(product => {
                const { barCode, productCode } = product;
                if (!barCodeMap.has(barCode)) {
                    barCodeMap.set(barCode, new Set([productCode]));
                } else {
                    const codes = barCodeMap.get(barCode);
                    codes.add(productCode);
                    if (codes.size > 1) {
                        duplicateBarCodes.add(barCode);
                    }
                }
            });

            // 如果存在重复条码且商品列表不为空，返回true
            return duplicateBarCodes.size > 0 && this.productList.length > 0;
        },

        // 商品行单击事件（仅展示详情，不触发复核）
        handleProductRowClick({ row }) {
            this.selectProductForDisplay(row);
            // 可选：显示选择提示（仅在开发模式下显示，避免干扰正常使用）
            if (process.env.NODE_ENV === 'development') {
                console.log(`已选择商品: ${row.productName} (${row.productCode})，可进行异常提交操作`);
            }
        },

        // 商品点击事件（双击触发复核）
        handleProductClick({ row }) {
            this.selectProduct(row);
            // 如果商品需要监管码扫描，打开追溯码扫描弹窗
            if (row.whetherRegulatory === 1 && row.reviewNumber > 0) {
                this.openTracingCodeDialog(row);
            } else {
                // 直接进行复核
                this.reviewProduct(row);
            }
        },

        // 已扫商品行单击事件（仅展示详情）
        handleScannedProductRowClick({ row }) {
            this.selectProductForDisplay(row);
            // 可选：显示选择提示（仅在开发模式下显示，避免干扰正常使用）
            if (process.env.NODE_ENV === 'development') {
                console.log(`已选择已复核商品: ${row.productName} (${row.productCode})，可进行异常提交操作`);
            }
        },

        // 复核商品
        reviewProduct(product) {
            const inputQuantity = parseInt(this.formData.quantity) || 0;
            // 计算同品不同批商品的复核数量汇总作为原始数量
            const originalQuantity = parseInt(this.calculateSameProductTotalQuantity(product)) || 0;

            // 检查数量差异
            if (inputQuantity !== originalQuantity) {
                this.handleQuantityDifference(product, inputQuantity, originalQuantity);
                return;
            }

            // 数量一致，直接复核
            this.executeReview(product, inputQuantity);
        },

        // 处理数量差异
        handleQuantityDifference(product, inputQuantity, originalQuantity) {
            // 检查商品是否有多个批号
            const productBatches = this.getProductBatches(product);

            if (productBatches.length <= 1) {
                // 单批号商品，自动处理差异
                this.handleSingleBatchDifference(product, inputQuantity, originalQuantity);
            } else {
                // 多批号商品，打开差异处理弹窗
                this.openQuantityDifferenceDialog(product, inputQuantity, productBatches);
            }
        },

        // 获取商品批号信息（修复：从商品列表中查找同品不同批商品）
        getProductBatches(product) {
            if (!product || !product.productCode) {
                return [];
            }

            const batches = [];

            // 从未复核商品列表中查找所有相同商品编码的商品
            this.productList.forEach(item => {
                if (item.productCode === product.productCode) {
                    batches.push({
                        productCode: item.productCode,
                        productName: item.productName,
                        batchNumber: item.batchNumber || "默认批号",
                        reviewNumber: parseInt(item.reviewNumber) || 0,
                        specifications: item.specifications || "",
                        packingUnit: item.packingUnit || "个",
                        producingArea: item.producingArea || "",
                        manufacturer: item.manufacturer || ""
                    });
                }
            });

            // 如果没有找到批号信息，创建默认批号
            if (batches.length === 0) {
                batches.push({
                    productCode: product.productCode,
                    productName: product.productName,
                    batchNumber: product.batchNumber || "默认批号",
                    reviewNumber: parseInt(product.reviewNumber) || 1,
                    specifications: product.specifications || "",
                    packingUnit: product.packingUnit || "个",
                    producingArea: product.producingArea || "",
                    manufacturer: product.manufacturer || ""
                });
            }

            return batches;
        },

        // 处理单批号商品的数量差异
        handleSingleBatchDifference(product, inputQuantity, originalQuantity) {
            const difference = inputQuantity - originalQuantity;
            const exceptionCause = difference > 0 ? "2" : "1"; // 2:多货, 1:少货

            // 构建异常提交数据
            const exceptionData = [{
                mergeOrderCode: this.formData.mergeOrderCode,
                productCode: product.productCode,
                batchNumber: product.batchNumber || "默认批号",
                exceptionNumber: Math.abs(difference),
                exceptionCause: exceptionCause,
                exceptionReason: "1", // 默认原因
                specifications: product.specifications,
                packingUnit: product.packingUnit,
                producingArea: product.producingArea,
                manufacturer: product.manufacturer
            }];

            // 自动提交差异
            submitException(exceptionData).then((res) => {
                const { code, msg } = res;
                if (code === 0) {
                    this.$message.success(`${difference > 0 ? '多拣' : '短拣'}差异已自动提交`);
                    // 继续复核流程
                    this.executeReview(product, inputQuantity);
                } else {
                    this.$message.error(msg);
                    // 复核失败，重置数量输入框
                    this.formData.quantity = originalQuantity.toString();
                    this.$refs.quantity.focus();
                }
            });
        },

        // 打开数量差异处理弹窗
        openQuantityDifferenceDialog(product, inputQuantity, batches) {
            const productData = {
                ...product,
                batches: batches,
                mergeOrderCode: this.formData.mergeOrderCode,
                // 添加汇总的复核数量，用于弹窗中的校验
                totalReviewNumber: batches.reduce((sum, batch) => sum + (parseInt(batch.reviewNumber) || 0), 0)
            };

            this.$refs.quantityDifferenceDialog.open(productData, inputQuantity);
        },

        // 执行复核
        // 修复说明：移除了手动数据移动逻辑，完全依赖数据分流来更新UI状态
        // 这样确保同品同批商品的复核状态与后端接口保持一致
        executeReview(product, quantity) {
            // 参考原页面逻辑，复核前先选中所有相同商品编码的商品行
            this.selectSameProductRows(product.productCode);

            const params = {
                productCode: product.productCode,
                orderCode: this.formData.orderCode,
                mergeOrderCode: this.formData.mergeOrderCode,
                quantity: quantity,
                maxMediumPacking: product.maxMediumPacking || 0  // 新增必需参数：最大中包装数量
            };

            review(params).then(res => {
                const { code, msg, result } = res;
                if (code === 0) {
                    this.$message.success(msg || "复核成功");
                    this.updateStats(result);

                    // 保存当前复核的商品信息，用于复核后显示
                    const reviewedProduct = {
                        ...product,
                        quantity: quantity,
                        reviewStatus: 2 // 标记为已复核
                    };

                    // 移除手动数据移动逻辑，完全依赖数据分流来更新UI状态
                    // 刷新商品列表（数据分流会自动处理已复核商品的移动）
                    // 传递false参数，避免自动选择第一个商品
                    this.loadProductList(false).then(() => {
                        // 复核成功后，显示刚复核的商品信息
                        this.selectReviewedProduct(reviewedProduct);

                        // 延迟清空数量输入框，让用户能看到复核结果
                        setTimeout(() => {
                            this.formData.quantity = "";
                        }, 500);
                    });

                    // 语音播报
                    if (result && result.ttsText) {
                        doTTS(result.ttsText);
                    }

                    // 复核成功后的光标跳转逻辑
                    this.setRowReviewStates();
                } else {
                    this.$message.error(msg);
                    // 复核失败时清空条码并聚焦（与原页面一致）
                    this.formData.barCode = "";
                    this.$refs.barCode.focus();
                }
            });
        },

        // 选中所有相同商品编码的商品行（参考原页面checkAndPosition1方法逻辑）
        selectSameProductRows(productCode) {
            if (!productCode) {
                return;
            }

            // 获取所有商品行
            const tableRows = this.productList;
            let totalQuantity = 0;

            // 遍历商品列表，选中所有相同商品编码的行
            tableRows.forEach((product) => {
                if (productCode === product.productCode) {
                    // 累计相同商品编码的总数量
                    totalQuantity += product.reviewNumber || 0;

                    // 这里可以添加视觉反馈，比如高亮显示相关行
                    // 由于使用的是vxe-table，可以通过设置行样式来实现
                }
            });

            // console.log(`选中商品编码 ${productCode} 的所有行，总数量: ${totalQuantity}`);
            return totalQuantity;
        },

        // 打开追溯码扫描弹窗
        openTracingCodeDialog(product) {
            // 获取需要扫描电子监管码的商品列表
            const goodsList = this.productList.filter(item =>
                item.productCode === product.productCode &&
                item.reviewNumber > 0
            );

            if (goodsList.length === 0) {
                this.$message.warning("请获取需要扫描电子监管码的商品！");
                return;
            }

            const params = {
                allocationCode: product.allocationCode,
                productCode: product.productCode,
                goods: goodsList,
                orderCode: product.orderCode,
                mergeOrderCode: this.formData.mergeOrderCode,
                largeCategoryCode: product.largeCategoryCode,
            };

            this.$refs.tracingCodeDialog.open(params);
            this.hasDialog = true;
        },

        // 追溯码扫描完成回调
        handleTracingCodeComplete(data) {
            // 更新统计数据
            this.updateStats(data.result);

            // 刷新商品列表，数据分流会自动处理已复核商品的移动
            // 传递false参数，避免自动选择第一个商品
            this.loadProductList(false).then(() => {
                // 复核成功后，显示刚复核的商品信息
                if (data.productInfo) {
                    this.selectReviewedProduct(data.productInfo);

                    // 延迟清空数量输入框，让用户能看到复核结果
                    setTimeout(() => {
                        this.formData.quantity = "";
                    }, 500);
                }
            });

            this.hasDialog = false;
        },

        // 商品条码扫描
        handleBarCodeScan() {
            if (!this.formData.barCode.trim()) {
                this.$message.warning("请输入商品条码");
                return;
            }

            // 检查是否存在多编码商品（参考原页面762-771行）
            if (this.checkForDuplicateBarCodes()) {
                this.$nextTick(() => {
                    this.$alert('商品有多个编码，请手动点击复核', '提示', {
                        confirmButtonText: '确定',
                        type: 'warning',
                    }).then(() => {
                        this.formData.barCode = "";
                        this.$refs.barCode.select();
                        return true;
                    });
                });
                return;
            }

            const barCode = this.formData.barCode;
            let matchedProduct = null;

            // 在商品列表中查找匹配的商品
            for (let i = 0; i < this.productList.length; i++) {
                const product = this.productList[i];
                const barCodeArr = product.barCode ? product.barCode.split(",") : [];

                // 检查条码或DI码是否匹配
                for (let j = 0; j < barCodeArr.length; j++) {
                    if (barCode === barCodeArr[j] || barCode === product.di) {
                        matchedProduct = product;
                        break;
                    }
                }

                if (matchedProduct) break;
            }

            if (matchedProduct) {
                // 检查商品是否已复核
                if (matchedProduct.reviewStatus === 2) {
                    this.selectProduct(matchedProduct);
                    this.$message.warning("该条形码对应的商品已复核！");
                    // 选中商品条码输入框内容（与原页面一致）
                    this.$refs.barCode.select();
                    doTTS("商品已复核");
                    return;
                }

                // 商品行上浮到第一行并高亮显示
                this.moveProductToTop(matchedProduct);

                // 选中商品并显示详情
                this.selectProduct(matchedProduct);

                // 自动填充数量输入框
                this.formData.quantity = matchedProduct.reviewNumber || "";

                // 如果需要扫描电子监管码
                if (matchedProduct.whetherRegulatory === 1 && matchedProduct.reviewNumber > 0) {

                    this.openTracingCodeDialog(matchedProduct);
                } else {
                    // 直接复核
                    this.reviewProduct(matchedProduct);
                }

                this.formData.barCode = "";
                this.$refs.barCode.select();
            } else {
                // 找不到匹配的商品，尝试作为随货同行单号处理
                this.handleAccompanyingDocumentScan(barCode);
            }
        },

        // 随货同行单扫码处理（参考原页面checkAndPosition方法）
        handleAccompanyingDocumentScan(barCode) {
            const params = {
                productCode: barCode, // 将扫描的内容作为productCode参数
                mergeOrderCode: this.formData.mergeOrderCode,
                maxMediumPacking: 0  // 新增必需参数：随货同行单扫码时设置默认值
            };

            review(params).then(res => {
                const { code, msg, result } = res;
                if (code === 0) {
                    this.$message.success(msg || "随货同行单复核成功");

                    // 更新统计数据
                    this.updateStats(result);

                    // 刷新商品列表
                    this.loadProductList();

                    // 清空条码输入框并聚焦
                    this.formData.barCode = "";
                    this.$refs.barCode.select();

                    // 语音播报（如果有TTS功能）
                    if (result && result.ttsText) {
                        doTTS(result.ttsText);
                    }
                } else {
                    this.$message.error(msg || "找不到该条形码对应的商品！");
                    this.formData.barCode = "";
                    this.$refs.barCode.select();
                }
            }).catch(() => {
                // this.$message.warning("找不到该条形码对应的商品！");
                this.formData.barCode = "";
                this.$refs.barCode.select();
            });
        },

        // 耗材码扫描
        handleMaterialCodeScan() {
            if (!this.formData.materialCode.trim()) {
                this.$message.warning("请输入耗材码");
                return;
            }

            checkParts({
                boxCode: this.formData.materialCode
            }).then(res => {
                const { code, msg, result } = res;
                if (code === 0) {
                    // 添加耗材到表格中
                    const materialData = {
                        materialCode: this.formData.materialCode,
                        materialName: result?.boxType // 从接口返回的数据中获取耗材名称
                    };
                    this.addMaterial(materialData);

                    this.formData.materialCode = "";
                    // 重新加载商品列表以更新状态
                    this.loadProductList();

                    // 耗材码保存成功后的光标跳转逻辑（与原页面一致）
                    this.$nextTick(() => {
                        // 检查是否所有商品都已复核且统计数据匹配
                        const reviewedCount = this.productList.filter(item => item.reviewStatus === 2).length;
                        const allReviewed = reviewedCount === this.productList.length;
                        const statsMatch = this.packageStats.packedTotal === this.packageStats.totalProducts;
                        if (allReviewed && statsMatch) {
                            // 如果所有商品都已复核且统计数据匹配，跳转到耗材码输入框
                            this.$refs.materialCode.focus();
                        } else {
                            // 否则跳转到商品条码输入框
                            this.$refs.barCode.focus();
                        }
                    });
                } else {
                    this.$message.error(msg);
                    this.formData.materialCode = "";
                    this.$refs.materialCode.focus();
                }
            });
        },

        // 复核确认
        handlePackageConfirm() {
            if (!this.formData.erpOrderCode) {
                this.$message.error("请先输入销售单号");
                return;
            }

            this.mainLoading = true;
            // 构建耗材码列表
            const boxCodeList = this.materialsData.map(item => item.materialCode);

            reviewConfirm({
                orderCode: this.formData.orderCode,
                mergeOrderCode: this.formData.mergeOrderCode,
                checkCancelOrder: this.checkCancelOrder === 1 ? undefined : 0, // 添加取消订单检查参数
                boxCodeList: boxCodeList // 传递实际的耗材码列表
            }).then(res => {
                this.mainLoading = false;
                const { code, msg, result } = res;
                this.checkCancelOrder = 1; // 重置为默认值

                if (code === 0) {
                    if (result.isPrint == 1) {
                        printNew(result.printContent)
                    }
                    this.$message.success(msg || "复核确认成功");
                    this.clearPageData();
                    this.$nextTick(() => {
                        this.$refs.erpOrderCode.focus();
                    });
                } else if (code === 2000) {
                    // 需要用户确认的情况（与原页面逻辑一致）
                    this.$confirm(msg, "提示", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning",
                    }).then(() => {
                        // 用户确认后设置为0，跳过检查并重新提交
                        this.checkCancelOrder = 0;
                        this.handlePackageConfirm();
                    }).catch(() => {
                        // 用户取消也重置状态
                        this.checkCancelOrder = 0;
                    });
                } else if (code === 2001) {
                    // 清空页面并聚焦到销售单号输入框
                    this.clearPageData();
                    this.$refs.erpOrderCode.focus();
                    this.$message.error(msg);
                } else if (code === 2002) {
                    // 特殊错误处理（与原页面一致）
                    this.$message({
                        message: msg,
                        type: "warning",
                        onClose() {
                            window.parent.location.href = "/logout";
                        },
                    });
                } else {
                    this.$message.error(msg);
                }
            }).finally(() => {
                this.mainLoading = false;
            });
        },

        // 查看任务
        handleViewTasks() {
            this.$refs.viewTasksDialog.open();
            this.hasDialog = true;
        },

        // 异常提交
        handleExceptionSubmit() {
            // 检查是否有当前选中的商品
            if (!this.currentProduct || !this.currentProduct.productCode) {
                // 如果没有选中商品，提供选择：单个商品异常提交或批量异常提交
                // this.$confirm('未选择具体商品，是否对所有未复核商品进行异常提交？', '异常提交确认', {
                //     confirmButtonText: '批量异常提交',
                //     cancelButtonText: '取消',
                //     type: 'warning'
                // }).then(() => {
                //     // 用户确认批量异常提交
                //     if (this.productList.length > 0) {
                //         this.$refs.exceptionSubmitDialog.open(this.productList, this.formData.mergeOrderCode);
                //         this.hasDialog = true;
                //     } else {
                //         this.$message.warning("没有可异常提交的商品！");
                //     }
                // }).catch(() => {
                //     // 用户取消，提示选择商品
                //     this.$message.info("请先点击选择需要异常提交的商品！");
                // });
                this.$message.warning("请先点击选择需要异常提交的商品！");
                return;
            }

            // 查找当前选中商品的完整数据
            const selectedProduct = this.findSelectedProductData();

            if (selectedProduct) {
                // 传递选中商品的数组（保持与原页面接口一致）
                this.$refs.exceptionSubmitDialog.open([selectedProduct], this.formData.mergeOrderCode);
                this.hasDialog = true;
            } else {
                this.$message.warning("未找到选中商品的详细信息！");
            }
        },

        // 查找当前选中商品的完整数据
        findSelectedProductData() {
            if (!this.currentProduct || !this.currentProduct.productCode) {
                return null;
            }

            // 首先在未复核商品列表中查找
            let selectedProduct = this.productList.find(product =>
                product.productCode === this.currentProduct.productCode &&
                product.barCode === this.currentProduct.barCode
            );

            // 如果未复核列表中没有，则在已复核商品列表中查找
            if (!selectedProduct) {
                selectedProduct = this.scannedProductsData.find(product =>
                    product.productCode === this.currentProduct.productCode &&
                    product.barCode === this.currentProduct.barCode
                );
            }

            return selectedProduct;
        },

        // 更换承运商（与原页面逻辑一致）
        handleCarrierChange() {
            if (!this.formData.erpOrderCode) {
                this.$message.error("请输入销售单号");
                return;
            }

            // 调用API获取承运商信息
            changeCarrier({ orderCode: this.formData.erpOrderCode }).then(res => {
                const { code, msg, result } = res;
                if (code === 0 && result) {
                    // 打开确认弹窗，显示承运商更换信息
                    this.$refs.dialogChangeCYSAlert.open(this.formData.erpOrderCode, result);
                } else {
                    this.$message.error(msg);
                }
            });
        },

        // 刷新页面
        handleRefresh() {
            this.clearPageData();
            this.$refs.erpOrderCode.focus();
        },

        // 弹窗关闭处理
        handleDialogClose() {
            this.hasDialog = false;
            this.$refs.erpOrderCode.focus();
        },

        // 查看任务弹窗关闭后的光标跳转
        closeViewTasksDialog() {
            this.hasDialog = false;
            this.$refs.erpOrderCode.focus();
        },

        // 开启耗材码输入
        enableMaterialCodeInput() {
            // this.addBoxCodeOpen = true;
            this.$nextTick(() => {
                this.$refs.materialCode.focus();
            });
        },

        // 异常提交弹窗关闭后的处理
        closeExceptionSubmitDialog() {
            this.hasDialog = false;
            this.isSearchErp = false;
            this.$refs.erpOrderCode.focus();
            this.loadProductList(); // 刷新商品列表
        },

        // 清空页面数据
        clearPageData() {
            this.formData = {
                erpOrderCode: "",
                materialCode: "",
                barCode: "",
                quantity: "", // 清空数量输入框
                orderCode: "",
                allocationCode: "",
                mergeOrderCode: "",
                consolidationCode: "",
                supervisoryCodeAcquisition: "",
                orderNumberCancelled: "",
                inreviewProductNumber: ""
            };

            this.currentProduct = {
                productCode: "",
                productName: "",
                productNumber: "",
                packingUnit: "",
                image: "",
                // 重置存储属性和赠品相关字段
                storageAttributesExt: "",
                productGiftsName: "",
                productGiftsNumber: "",
                giftsPackingUnit: ""
            };

            this.productList = [];
            this.orderStats = { totalCount: 0, checkedCount: 0 };
            this.packageStats = { packedTotal: 0, unpackedTotal: 0, totalProducts: 0 };
            this.orderInfo = {
                expressType: "",
                orderType: "",
                carrierName: "",
                attributes: "",
                isMajorClients: false
            }; // 清空订单信息
            this.detailStats = [];
            this.materialsData = []; // 清空耗材数据
            this.scannedProductsData = []; // 清空已扫商品数据

            // 重置输入框禁用状态
            this.isSearchErp = false;
            // this.addBoxCodeOpen = false;

            // 清空数据后聚焦到销售单号输入框
            this.$nextTick(() => {
                this.$refs.erpOrderCode.focus();
            });
        },

        // 更新统计数据（参考原页面setRightNumber方法）
        updateStats(result) {
            this.orderStats = {
                totalCount: result.orderNumberAll || 0,   // 随货同行单总数量
                checkedCount: result.orderNumber || 0     // 随货同行单已检数量
            };

            this.packageStats = {
                packedTotal: result.productNumber || 0,
                unpackedTotal: result.productNumberAll - result.productNumber || 0,
                totalProducts: result.productNumberAll || 0
            };

            // 更新订单信息（承运商名称和大客户标识）
            if (result.carrierName !== undefined) {
                this.orderInfo.carrierName = result.carrierName;
            }
            if (result.isMajorClients !== undefined) {
                this.orderInfo.isMajorClients = result.isMajorClients;
            }
        },

        // 商品表格行样式（与原页面cellClassName方法完全一致）
        productCellClassName({ row }) {
            let rowName = "";

            // 根据复核状态设置行颜色
            if (row.reviewStatus == 2) {
                rowName = "cell-green"; // 已复核
            } else if (row.exceptionStatus !== null) {
                rowName = "cell-red"; // 异常
            } else {
                rowName = "cell-white"; // 待复核
            }

            // 如果是当前选中的商品，设置蓝色背景（使用currentProduct替代原页面的checkRow）
            if (this.currentProduct && this.currentProduct.barCode === row.barCode) {
                rowName = "cell-blue";
            }

            return rowName;
        },

        scannedProductCellClassName({ row }) {
            let rowName = "";

            // // 根据复核状态设置行颜色
            // if (row.reviewStatus == 2) {
            //     rowName = "cell-green"; // 已复核
            // } else if (row.exceptionStatus !== null) {
            //     rowName = "cell-red"; // 异常
            // } else {
            //     rowName = "cell-white"; // 待复核
            // }

            // 如果是当前选中的商品，设置蓝色背景（使用currentProduct替代原页面的checkRow）
            if (this.currentProduct && this.currentProduct.barCode === row.barCode) {
                rowName = "cell-blue";
            }

            return rowName;
        },



        // 复核状态格式化
        reviewStatusFormatter({ cellValue }) {
            const statusMap = {
                0: "待复核",
                1: "复核中",
                2: "已复核"
            };
            return statusMap[cellValue] || "未知";
        },

        // 未装数格式化器（装箱总数 - 已装数）
        remainingQuantityFormatter({ row }) {
            // 如果数据中直接有unpackedCount字段，优先使用它
            if (row.unpackedCount !== undefined) {
                return row.unpackedCount;
            }
            // 否则通过计算得出：总数量 - 已装箱数量
            const total = row.reviewNumber || 0;
            const packed = row.packedCount || 0;
            const remaining = total - packed;
            return remaining >= 0 ? remaining : 0;
        },

        // 已扫商品信息表格：未装箱数量格式化器
        unpackedCountFormatter({ row }) {
            // 如果数据中直接有unpackedCount字段，使用它
            if (row.unpackedCount !== undefined) {
                return row.unpackedCount;
            }
            // 否则通过计算得出：总数量 - 已装箱数量
            const total = row.reviewCount || row.reviewNumber || 0;
            const packed = row.packedCount || 0;
            const unpacked = total - packed;
            return unpacked >= 0 ? unpacked : 0;
        },

        // 获取特殊属性（红色显示）
        getAttributes() {
            if (this.productList.length === 0) {
                return "";
            }
            return this.productList[0].attributes
                ? "(" + this.productList[0].attributes + ")"
                : "";
        },

        // 设置复核后的状态和光标跳转（与原页面完全一致）
        setRowReviewStates() {
            // if (this.productList.length > 0) {
            // 如果还有商品需要复核，清空条码输入框并聚焦
            this.formData.barCode = "";
            this.$refs.barCode.focus();
            // } else {
            //     // 如果所有商品都已复核，重置状态并聚焦到销售单号输入框
            //     this.$refs.erpOrderCode.focus();
            //     this.isSearchErp = false;
            // }
        },

        // 删除耗材记录
        deleteMaterial(row, rowIndex) {
            this.$confirm('确定要删除这条耗材记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 从数据数组中移除对应的耗材记录
                this.materialsData.splice(rowIndex, 1);
                this.$message.success('删除成功');

                // 确保删除后表头固定效果正常
                this.$nextTick(() => {
                    this.ensureTableHeaderFixed();
                });

                // 重新计算表格高度，确保滚动条正确显示
                this.$nextTick(() => {
                    if (this.$refs.headerMaterialsTable) {
                        this.$refs.headerMaterialsTable.recalculate();
                    }
                });
            }).catch(() => {
                // 用户取消删除
            });
        },

        // 添加耗材记录（供扫描耗材码功能调用）
        addMaterial(materialData) {
            // 检查是否已存在相同的耗材编码
            // const existingIndex = this.materialsData.findIndex(item => item.materialCode === materialData.materialCode);
            // if (existingIndex !== -1) {
            //     this.$message.warning('该耗材已存在');
            //     return;
            // }

            // 添加新的耗材记录
            this.materialsData.push({
                materialCode: materialData.materialCode,
                materialName: materialData.materialName
            });
            this.$message.success('耗材添加成功');

            // 重新计算表格高度，确保滚动条正确显示
            this.$nextTick(() => {
                if (this.$refs.headerMaterialsTable) {
                    this.$refs.headerMaterialsTable.recalculate();
                }
                // 确保添加后表头固定效果正常
                this.ensureTableHeaderFixed();
            });
        },

        // 添加已扫商品记录
        addScannedProduct(productData) {
            // 检查是否已存在相同的商品记录
            // 修复：添加批号比较，确保同品不同批商品能够正确显示
            const existingIndex = this.scannedProductsData.findIndex(item =>
                item.productCode === productData.productCode &&
                item.productNumber === productData.productNumber &&
                item.batchNumber === productData.batchNumber
            );

            if (existingIndex !== -1) {
                // 如果已存在，更新记录
                this.scannedProductsData[existingIndex] = {
                    ...this.scannedProductsData[existingIndex],
                    ...productData,
                    scanTime: new Date().toLocaleString() // 更新扫描时间
                };
            } else {
                // 如果不存在，添加新记录
                this.scannedProductsData.push({
                    ...productData,
                    scanTime: new Date().toLocaleString() // 添加扫描时间
                });
            }
        },

        // 将已复核商品移动到已扫商品信息表格
        moveProductToScanned(product, actualQuantity = null) {
            // 创建已复核商品的数据副本
            const scannedProduct = { ...product };

            // 如果提供了实际数量，说明是手动复核或商品条码扫码复核
            if (actualQuantity !== null) {
                scannedProduct.packedCount = actualQuantity;  // 使用实际输入的数量
                scannedProduct.unpackedCount = 0;             // 默认为0
            } else {
                // 如果没有提供实际数量，保持原有的装箱状态（用于初始化数据分流）
                // 在 processProductDataFlow 中已经设置了正确的值
            }

            // 检查是否已存在，如果存在则更新，否则添加
            // 修复：添加批号比较，确保同品不同批商品能够正确显示
            const existingIndex = this.scannedProductsData.findIndex(
                item => item.productCode === product.productCode &&
                    item.productNumber === product.productNumber &&
                    item.batchNumber === product.batchNumber
            );

            if (existingIndex >= 0) {
                this.scannedProductsData.splice(existingIndex, 1, scannedProduct);
            } else {
                this.scannedProductsData.push(scannedProduct);
            }
        },

        // 已扫商品信息表格双击事件处理（取消复核）
        handleScannedProductDblClick({ row }) {
            // 确认是否要取消复核
            this.$confirm('确定要取消该商品的复核吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.unReview(row);
            }).catch(() => {
                // 用户取消操作
            });
        },

        // 取消复核（参考原页面unReview方法）
        unReview(row) {
            const params = {
                allocationCode: row.allocationCode,
                productCode: row.productCode,
                orderCode: row.orderCode,
                cancel: 1, // 取消复核标志
                mergeOrderCode: this.formData.mergeOrderCode,
                maxMediumPacking: row.maxMediumPacking || 0  // 新增必需参数：最大中包装数量
            };

            review(params).then(res => {
                const { code, msg, result } = res;
                if (code === 0) {
                    this.$message.success(msg || "取消复核成功");

                    // 从已扫商品信息表格中移除
                    // 修复：添加批号比较，确保只移除对应批号的商品记录
                    const index = this.scannedProductsData.findIndex(
                        item => item.productCode === row.productCode &&
                            item.productNumber === row.productNumber &&
                            item.batchNumber === row.batchNumber
                    );
                    if (index >= 0) {
                        this.scannedProductsData.splice(index, 1);
                    }

                    // 更新统计数据
                    this.updateStats(result);

                    // 刷新商品列表（会重新显示该商品）
                    this.loadProductList();

                    // 取消复核后的光标跳转逻辑
                    this.setRowReviewStates();
                } else {
                    this.$message.error(msg);
                    // 取消复核失败时聚焦到商品条码输入框
                    this.formData.barCode = "";
                    this.$refs.barCode.focus();
                }
            });
        },

        // 差异提交成功回调
        handleDifferenceSubmitted(data) {
            // 使用实际数量继续复核流程
            const product = this.currentProduct;
            this.executeReview(product, data.actualQuantity);
        },

        // 差异验证失败回调
        handleValidationFailed() {
            // 验证失败，光标返回到数量输入框
            this.$nextTick(() => {
                const quantityInput = this.$el.querySelector('input[placeholder="请输入数量"]');
                if (quantityInput) {
                    quantityInput.focus();
                    quantityInput.select();
                }
            });
        },

        // 差异处理取消回调
        handleDifferenceCancelled() {
            // 用户取消差异处理，恢复原始数量
            if (this.currentProduct.reviewNumber) {
                this.formData.quantity = this.currentProduct.reviewNumber.toString();
            }
            this.$nextTick(() => {
                const quantityInput = this.$el.querySelector('input[placeholder="请输入数量"]');
                if (quantityInput) {
                    quantityInput.focus();
                    quantityInput.select();
                }
            });
        },

        // 更新已扫商品记录（用于复核操作后更新数据）
        // 修复：支持批号参数，确保更新正确的批号商品
        updateScannedProduct(productCode, updateData, batchNumber = null) {
            const index = this.scannedProductsData.findIndex(item => {
                if (batchNumber) {
                    // 如果提供了批号，则同时匹配商品编码和批号
                    return item.productCode === productCode && item.batchNumber === batchNumber;
                } else {
                    // 如果没有提供批号，则只匹配商品编码（保持向后兼容）
                    return item.productCode === productCode;
                }
            });

            if (index !== -1) {
                this.scannedProductsData[index] = {
                    ...this.scannedProductsData[index],
                    ...updateData,
                    scanTime: new Date().toLocaleString() // 更新扫描时间
                };
            }
        },

        // 处理窗口大小变化（带防抖机制）
        handleResize() {
            // 清除之前的定时器
            if (this.resizeTimer) {
                clearTimeout(this.resizeTimer);
            }

            // 设置防抖定时器，300ms后执行
            this.resizeTimer = setTimeout(() => {
                this.doResize();
            }, 300);
        },

        // 实际执行resize处理的方法
        doResize() {
            // 强制重新计算计算属性
            this.$forceUpdate();

            // 动态调整右区块布局
            this.adjustRightBlockLayout();

            // 强制重新应用布局样式，确保顶部操作区与主内容区对齐
            this.$nextTick(() => {
                // 获取容器元素
                const headerSection = this.$el.querySelector('.header-section');
                const mainContent = this.$el.querySelector('.main-content');

                if (headerSection && mainContent) {
                    // 强制重新计算布局 - 使用更温和的方式
                    const headerDisplay = getComputedStyle(headerSection).display;
                    const mainDisplay = getComputedStyle(mainContent).display;

                    // 临时改变display属性触发重新布局
                    headerSection.style.display = 'block';
                    headerSection.offsetHeight; // 触发重排
                    headerSection.style.display = headerDisplay;

                    mainContent.style.display = 'block';
                    mainContent.offsetHeight; // 触发重排
                    mainContent.style.display = mainDisplay;
                }

                // 重新调整表格大小
                if (this.$refs.productTable) {
                    this.$refs.productTable.recalculate();
                }
                if (this.$refs.headerMaterialsTable) {
                    this.$refs.headerMaterialsTable.recalculate();
                }
                if (this.$refs.scannedProductsTable) {
                    this.$refs.scannedProductsTable.recalculate();
                }
            });
        },

        // 动态调整右区块布局
        adjustRightBlockLayout() {
            const windowWidth = window.innerWidth;
            const rightBlock = this.$el.querySelector('.header-right-block');
            const typeInfoSection = this.$el.querySelector('.type-info-section');
            const rankingSection = this.$el.querySelector('.ranking-info-section');

            if (!rightBlock) return;

            // 根据窗口宽度动态调整布局
            if (windowWidth < 900) {
                // 小屏幕：隐藏复核排名，类型信息占更多空间
                if (rankingSection) {
                    rankingSection.style.display = 'none';
                }
                if (typeInfoSection) {
                    typeInfoSection.style.width = '35%';
                    typeInfoSection.style.minWidth = '140px';
                }
            } else if (windowWidth < 1200) {
                // 中等屏幕：显示复核排名但压缩尺寸
                if (rankingSection) {
                    rankingSection.style.display = 'flex';
                    rankingSection.style.width = '15%';
                    rankingSection.style.minWidth = '100px';
                }
                if (typeInfoSection) {
                    typeInfoSection.style.width = '25%';
                    typeInfoSection.style.minWidth = '160px';
                }
            } else {
                // 大屏幕：恢复默认布局
                if (rankingSection) {
                    rankingSection.style.display = 'flex';
                    rankingSection.style.width = '20%';
                    rankingSection.style.minWidth = '120px';
                }
                if (typeInfoSection) {
                    typeInfoSection.style.width = '25%';
                    typeInfoSection.style.minWidth = '200px';
                }
            }

            // 确保耗材表格的表头固定效果
            this.ensureTableHeaderFixed();
        },

        // 确保表头固定效果
        ensureTableHeaderFixed() {
            this.$nextTick(() => {
                const headerWrapper = this.$el.querySelector('.materials-table-container .vxe-table--header-wrapper');
                const bodyWrapper = this.$el.querySelector('.materials-table-container .vxe-table--body-wrapper');

                if (headerWrapper && bodyWrapper) {
                    // 确保表头固定在顶部
                    headerWrapper.style.position = 'sticky';
                    headerWrapper.style.top = '0';
                    headerWrapper.style.zIndex = '10';
                    headerWrapper.style.background = 'white';

                    // 确保表体可以独立滚动
                    bodyWrapper.style.overflowY = 'auto';
                    bodyWrapper.style.overflowX = 'hidden';
                    bodyWrapper.style.maxHeight = 'calc(100% - 40px)';
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.new-review-package-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    overflow: hidden; // 防止整个容器出现滚动条

    // 强制确保布局比例正确应用 - 最高优先级
    .main-content {
        display: flex !important;

        .left-section {
            flex: 2 !important; // 强制左侧40%
            width: auto !important;
            min-width: 300px !important;
            max-width: none !important;
        }

        .right-area {
            flex: 3 !important; // 强制右侧60%
            width: auto !important;
            min-width: 0 !important;
            max-width: none !important;
        }
    }

    // 强制确保顶部操作区布局比例正确应用 - 最高优先级
    .header-section {
        display: flex !important;
        align-items: stretch !important;
        flex-wrap: nowrap !important; // 防止换行
        box-sizing: border-box !important;

        .header-left-block {
            flex: 2 !important; // 强制左区块40%，与主内容区保持一致
            width: auto !important;
            min-width: 300px !important; // 与主内容区左侧保持一致的最小宽度
            max-width: none !important;
            flex-shrink: 0 !important; // 防止收缩
            flex-grow: 2 !important; // 强制增长比例
            box-sizing: border-box !important;
        }

        .header-right-block {
            flex: 3 !important; // 强制右区块60%，与主内容区保持一致
            width: auto !important;
            min-width: 0 !important;
            max-width: none !important;
            flex-shrink: 0 !important; // 防止收缩
            flex-grow: 3 !important; // 强制增长比例
            box-sizing: border-box !important;
        }
    }

    .header-section {
        display: flex;
        align-items: stretch; // 确保两个区块高度一致
        padding: 2px; // 与主内容区保持一致的内边距
        background: #f5f5f5; // 与主容器背景色保持一致
        border-bottom: 1px solid #e8e8e8;
        flex-shrink: 0; // 确保header高度固定
        gap: 2px; // 与主内容区保持一致的间距

        // 左区块 (40%) - 对应左侧商品信息区的比例
        .header-left-block {
            display: flex;
            flex-direction: column;
            gap: 12px;
            flex: 2 !important; // 占2份，对应40%宽度，与主内容区保持一致
            background: white; // 与主内容区左侧区域保持一致
            padding: 16px;
            border-radius: 8px; // 与主内容区保持一致
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); // 与主内容区保持一致
            min-height: 120px; // 设置最小高度确保高度统一

            .input-group {
                display: flex;
                align-items: center;
                gap: 12px;

                label {
                    font-weight: 500;
                    color: #333;
                    min-width: 70px;
                }

                .sales-input {
                    width: 300px;
                }
            }

            // 耗材码输入框专用样式
            .material-code-group {
                margin-top: 8px;

                label {
                    min-width: 60px;
                }

                .el-input {
                    width: 250px;
                }
            }

            .order-info {
                display: flex;
                gap: 20px;

                .info-item {
                    font-size: 16px; // 增大标签文字字体大小
                    color: #666;
                    display: flex;
                    align-items: center;

                    .count {
                        font-weight: bold;
                        color: #409eff; // 默认数值颜色改为蓝色
                        margin-left: 4px; // 添加一点间距
                        font-size: 23px; // 调整数值字体大小为 23px

                        &.red {
                            color: #ff4757; // 更醒目的红色
                        }
                    }
                }
            }
        }

        // 右区块 (60%) - 对应右侧区域的比例
        .header-right-block {
            display: flex;
            flex-direction: row; // 改为水平布局
            gap: 12px; // 各部分之间的间距
            flex: 3 !important; // 占3份，对应60%宽度，与主内容区保持一致
            background: white; // 与主内容区右侧区域保持一致
            padding: 16px;
            border-radius: 8px; // 与主内容区保持一致
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); // 与主内容区保持一致
            min-height: 120px; // 设置最小高度确保与左区块高度统一
            align-items: stretch; // 确保各部分高度一致
            overflow: hidden; // 防止内容溢出
            min-width: 0; // 允许flex子项收缩

            // 第一部分：快递类型和订单类型信息区域
            .type-info-section {
                flex: 0 0 auto; // 自适应宽度
                min-width: 200px; // 最小宽度保证内容可读性
                max-width: 280px; // 最大宽度防止过度扩展
                width: 25%; // 占据25%的宽度
                display: flex;
                flex-direction: column;
                justify-content: center;
                padding-right: 8px; // 与下一部分的间距
                border-right: 1px solid #e8e8e8; // 添加分隔线
                overflow: hidden; // 防止内容溢出

                .type-info {
                    display: flex;
                    flex-direction: column; // 保持垂直布局
                    gap: 12px; // 垂直间距
                    align-items: flex-start;

                    .express-type,
                    .order-type {
                        font-size: 15px; // 适当调整字体大小
                        display: flex;
                        align-items: center;
                        width: 100%;

                        .label {
                            color: #666;
                            margin-right: 8px;
                            min-width: 70px; // 标签固定宽度
                        }

                        .value {
                            font-weight: 600; // 增加字体粗细
                            color: #409eff; // 数值颜色改为蓝色
                            font-size: 20px; // 数值字体大小
                            flex: 1; // 占据剩余空间
                        }
                    }
                }
            }

            // 第二部分：耗材录入表格区域
            .materials-section {
                flex: 1; // 占据主要空间
                min-width: 300px; // 最小宽度确保表格可用性
                display: flex;
                flex-direction: column;
                min-height: 0; // 允许收缩
                padding: 0 8px; // 左右内边距
                overflow: hidden; // 防止内容溢出

                .header-materials-area {
                    width: 100%;
                    display: flex;
                    flex-direction: column;
                    max-height: 140px; // 设置整个耗材区域的最大高度
                    overflow: hidden; // 防止区域被撑高

                    .materials-table-container {
                        width: 100%;
                        height: 140px; // 调整高度以适应新布局
                        max-height: 140px; // 设置最大高度
                        border-radius: 4px;
                        overflow: hidden; // 禁用容器滚动，使用表格内部滚动
                        border: 1px solid #e8e8e8;
                        flex-shrink: 0; // 防止容器被压缩
                        position: relative; // 确保sticky定位正确工作
                        background: white; // 确保背景色正确
                        display: flex; // 使用flex布局支持表格的flex结构
                        flex-direction: column; // 垂直排列

                        // 优化vxe-table样式，实现表头固定和表体独立滚动
                        :deep(.vxe-table) {
                            height: 100% !important;
                            max-height: 100% !important;
                            width: 100% !important;
                            border: none !important; // 移除表格边框，使用容器边框
                            display: flex !important; // 使用flex布局
                            flex-direction: column !important; // 垂直排列表头和表体
                        }

                        // 表头固定样式
                        :deep(.vxe-table--header-wrapper) {
                            position: sticky !important; // 使用sticky定位固定表头
                            top: 0 !important; // 固定在顶部
                            z-index: 10 !important; // 确保表头在表体之上
                            background: white !important; // 设置背景色避免透明
                            overflow: hidden !important; // 禁用表头滚动条
                            border-bottom: 1px solid #e8e8e8 !important;
                            flex-shrink: 0 !important; // 表头不收缩
                            height: 40px !important; // 固定表头高度
                            min-height: 40px !important;
                            max-height: 40px !important;
                        }

                        // 表体独立滚动样式
                        :deep(.vxe-table--body-wrapper) {
                            flex: 1 !important; // 占据剩余空间
                            overflow-y: auto !important; // 只允许垂直滚动
                            overflow-x: hidden !important; // 禁用水平滚动
                            max-height: calc(100% - 40px) !important; // 减去表头高度
                            min-height: 0 !important; // 允许收缩
                        }

                        // 主包装器样式优化
                        :deep(.vxe-table--main-wrapper) {
                            height: 100% !important;
                            display: flex !important;
                            flex-direction: column !important;
                            overflow: hidden !important; // 禁用主包装器滚动
                        }

                        // 表头内容区域
                        :deep(.vxe-table--header) {
                            overflow: hidden !important;
                            background: white !important; // 确保表头背景不透明
                        }

                        // 表体内容区域
                        :deep(.vxe-table--body) {
                            overflow: hidden !important; // 表体本身不滚动，由wrapper控制
                            flex: 1 !important;
                        }

                        // 表尾区域（如果存在）
                        :deep(.vxe-table--footer) {
                            overflow: hidden !important;
                            flex-shrink: 0 !important;
                        }

                        // 列宽设置
                        :deep(.vxe-table-column) {
                            min-width: 80px !important;
                        }

                        // 表头行样式
                        :deep(.vxe-table--header-wrapper .vxe-table--header tr) {
                            background: white !important;
                            position: relative !important;
                        }

                        // 表头单元格样式
                        :deep(.vxe-table--header-wrapper .vxe-table--header th) {
                            background: white !important;
                            position: relative !important;
                            border-bottom: 1px solid #e8e8e8 !important;
                        }

                        // 表体行样式优化
                        :deep(.vxe-table--body-wrapper .vxe-table--body tr) {
                            position: relative !important;
                        }

                        // 确保表体单元格不会影响表头
                        :deep(.vxe-table--body-wrapper .vxe-table--body td) {
                            position: relative !important;
                        }

                        // 修复可能的边框重叠问题
                        :deep(.vxe-table--border-line) {
                            display: none !important; // 隐藏可能干扰的边框线
                        }

                        // 自定义滚动条样式（仅针对表体）
                        :deep(.vxe-table--body-wrapper::-webkit-scrollbar) {
                            width: 6px;
                            height: 6px;
                        }

                        :deep(.vxe-table--body-wrapper::-webkit-scrollbar-track) {
                            background: #f1f1f1;
                            border-radius: 3px;
                        }

                        :deep(.vxe-table--body-wrapper::-webkit-scrollbar-thumb) {
                            background: #c1c1c1;
                            border-radius: 3px;
                        }

                        :deep(.vxe-table--body-wrapper::-webkit-scrollbar-thumb:hover) {
                            background: #a8a8a8;
                        }

                        // 容器级别的滚动条样式
                        &::-webkit-scrollbar {
                            width: 8px;
                            height: 8px;
                        }

                        &::-webkit-scrollbar-track {
                            background: #f5f5f5;
                            border-radius: 4px;
                        }

                        &::-webkit-scrollbar-thumb {
                            background: #d9d9d9;
                            border-radius: 4px;
                        }

                        &::-webkit-scrollbar-thumb:hover {
                            background: #bfbfbf;
                        }
                    }
                }
            }

            // 第三部分：复核排名信息控件（占位）
            .ranking-info-section {
                flex: 0 0 auto; // 自适应宽度
                min-width: 120px; // 最小宽度
                max-width: 180px; // 最大宽度
                width: 20%; // 占据20%的宽度
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                padding-left: 8px; // 与上一部分的间距
                border-left: 1px solid #e8e8e8; // 添加分隔线
                overflow: hidden; // 防止内容溢出

                .ranking-placeholder {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    background: #f8f9fa; // 浅灰色背景
                    border: 2px dashed #d9d9d9; // 虚线边框
                    border-radius: 6px;
                    min-height: 80px;

                    .placeholder-content {
                        text-align: center;
                        color: #999;

                        .placeholder-text {
                            display: block;
                            font-size: 14px;
                            font-weight: 500;
                            margin-bottom: 4px;
                        }

                        .placeholder-subtitle {
                            display: block;
                            font-size: 12px;
                            color: #ccc;
                        }
                    }
                }
            }
        }
    }

    .main-content {
        flex: 1;
        display: flex;
        gap: 2px;
        padding: 2px;
        overflow: hidden; // 防止主内容区溢出
        min-height: 0; // 允许flex子项收缩

        .left-section {
            flex: 2 !important; // 左侧区域占2份，实现2:3的比例（40%）
            background: white;
            border-radius: 8px;
            padding: 2px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow-y: auto; // 如果内容过多，显示滚动条

            .product-image-container {
                width: 100%;
                flex: 1; // 使用弹性布局占据可用空间
                min-height: 200px; // 设置最小高度
                max-height: 400px; // 设置最大高度
                border: 2px dashed #d9d9d9;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;

                .image-placeholder {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        max-width: 100%;
                        max-height: 100%;
                        object-fit: contain;
                    }

                    .no-image {
                        color: #999;
                        font-size: 16px;
                    }
                }
            }

            .product-info {
                flex-shrink: 0; // 防止商品信息区域被压缩
                flex: 1; // 商品信息区域占据剩余空间

                .info-row {
                    display: flex;
                    margin-bottom: 12px;

                    .label {
                        width: 80px;
                        color: #666;
                        flex-shrink: 0;
                    }

                    .value {
                        color: #333;
                        font-weight: 500;
                        flex: 1;
                        word-break: break-all;
                    }
                }
            }

            // 扫描输入区域样式
            .scan-input-area {
                margin: 0 0 20px 0;
                padding: 2px 0;
                border-top: 1px solid #e8e8e8;
                border-bottom: 1px solid #e8e8e8;
                flex-shrink: 0; // 防止输入区域被压缩

                .input-row {
                    display: flex;
                    gap: 16px;
                    align-items: flex-end;

                    .barcode-input-group {
                        flex: 2; // 占据较大宽度

                        label {
                            display: block;
                            margin-bottom: 8px;
                            color: #666;
                            font-weight: 500;
                            font-size: 14px;
                        }

                        .el-input {
                            width: 100%;
                        }
                    }

                    .quantity-input-group {
                        flex: 1; // 占据较小宽度

                        label {
                            display: block;
                            margin-bottom: 8px;
                            color: #666;
                            font-weight: 500;
                            font-size: 14px;
                        }

                        .el-input {
                            width: 100%;
                        }
                    }
                }
            }
        }

        .right-area {
            flex: 3 !important; // 右侧区域占3份，实现2:3的比例（60%）
            display: flex;
            flex-direction: column;
            gap: 2px;

            .right-section {
                flex: 1; // 商品列表区域占据右侧区域的上半部分
                background: white;
                border-radius: 8px;
                padding: 2px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                display: flex;
                flex-direction: column;
                overflow: hidden; // 防止右侧区域溢出
                min-height: 0; // 允许flex子项收缩

                .product-table-container {
                    flex: 1;
                    overflow: auto; // 只在表格容器内显示滚动条
                    min-height: 200px; // 设置最小高度，防止塌陷
                    max-height: 420px; // 设置最大高度，防止撑开容器
                }
            }

            .bottom-section {
                flex-shrink: 0; // 防止底部区域被压缩
                background: white;
                border-radius: 8px;
                padding: 2px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                max-height: 40vh; // 限制底部区域最大高度
                display: flex;
                flex-direction: column;
                overflow: hidden; // 防止整体溢出

                // 上方区域：操作按钮和统计信息
                .top-area {
                    flex-shrink: 0; // 固定高度，不被压缩
                    display: flex;
                    gap: 20px;
                    margin-bottom: 20px;

                    // 左侧区域：操作按钮和统计信息（占60%）
                    .left-area {
                        flex: 0 0 60%; // 固定占60%宽度
                        display: flex;
                        flex-direction: column;
                        gap: 16px;

                        .operation-area {
                            display: flex;
                            justify-content: flex-start; // 左对齐显示操作按钮
                            align-items: center;

                            .action-buttons {
                                display: flex;
                                gap: 12px;
                            }
                        }

                        .stats-area {
                            .stats-row {
                                display: flex;
                                gap: 24px; // 横向排列，增加间距
                                align-items: center;

                                .stat-item {
                                    display: flex;
                                    align-items: center;
                                    gap: 8px;
                                    font-size: 16px;

                                    .label {
                                        color: #666;
                                        font-weight: normal;
                                    }

                                    .value {
                                        font-weight: bold;
                                        color: #333;
                                        font-size: 23px; // 增大数值字体大小

                                        &.red {
                                            color: #f56c6c;
                                        }

                                        &.blue {
                                            color: #409eff;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 右侧区域：耗材录入表格（占40%）
                    .right-area {
                        flex: 0 0 40%; // 固定占40%宽度
                        display: flex;
                        flex-direction: column;
                        overflow: hidden; // 防止内容溢出

                        .materials-area {
                            flex: 0 0 auto; // 不允许扩展，使用内容高度
                            display: flex;
                            flex-direction: column;
                            max-height: 170px; // 减小整个耗材区域的最大高度
                            overflow: hidden; // 强制隐藏溢出内容

                            .materials-title {
                                font-size: 16px;
                                font-weight: 600;
                                color: #333;
                                margin-bottom: 12px;
                                padding-bottom: 8px;
                                border-bottom: 1px solid #e8e8e8;
                            }

                            .materials-table-container {
                                flex: 1;
                                border: 1px solid #e8e8e8;
                                border-radius: 6px;
                                overflow: hidden; // 隐藏容器滚动条，让表格内部处理滚动
                                max-height: 120px; // 进一步减小最大高度，节省垂直空间
                                height: 120px; // 设置固定高度，与表格高度保持一致
                            }
                        }
                    }
                }

                // 下方区域：已扫商品信息表格
                .bottom-area {
                    flex: 1; // 占满剩余空间
                    display: flex;
                    flex-direction: column;
                    min-height: 0; // 允许flex子项收缩

                    .scanned-products-area {
                        flex: 1;
                        display: flex;
                        flex-direction: column;

                        .scanned-products-title {
                            font-size: 16px;
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 12px;
                            padding-bottom: 8px;
                            border-bottom: 1px solid #e8e8e8;
                        }

                        .scanned-products-table-container {
                            flex: 1;
                            border: 1px solid #e8e8e8;
                            border-radius: 6px;
                            overflow: hidden; // 隐藏容器滚动条，让表格内部处理滚动
                            min-height: 200px; // 设置最小高度
                            max-height: 320px; // 设置最大高度，防止撑开容器
                        }
                    }
                }
            }
        }
    }

    // 表格样式优化
    :deep(.vxe-table) {
        height: 100% !important; // 确保表格占满容器高度

        .vxe-header--column {
            background-color: #f5f7fa;
            font-weight: 600;
        }
    }

    // 移除全局耗材表格样式，避免与右区块内部样式冲突
    // 所有样式统一在 .header-right-block .materials-table-container 中定义

    // 已扫商品表格特定样式
    .scanned-products-table-container {
        :deep(.vxe-table) {

            // 确保滚动条正确显示
            .vxe-table--body-wrapper {
                overflow-x: auto !important; // 启用横向滚动
                overflow-y: auto !important; // 启用纵向滚动
            }
        }
    }

    // 继续原有的表格样式
    :deep(.vxe-table) {
        .vxe-body--row:hover {
            background-color: #f0f9ff;
        }

        .vxe-body--row.row--current {
            background-color: #e6f7ff;
        }

        // 确保表格体能正确滚动
        .vxe-table--body-wrapper {
            overflow-y: auto !important;
            max-height: calc(100% - 40px) !important; // 减去表头高度
        }

        // 商品状态行样式（与原页面完全一致）
        .cell-green {
            background-color: lightgreen !important;
        }

        .cell-red {
            background-color: red !important;
        }

        .cell-blue {
            background-color: lightblue !important;
            font-weight: bold !important; // 选中商品行文字加粗
            font-size: 14px !important; // 选中商品行文字加大
        }

        .cell-white {
            background-color: white !important;
        }
    }

    // 按钮样式优化
    :deep(.el-button) {
        border-radius: 6px;
        font-weight: 500;

        &.el-button--primary {
            background: #409eff;
            border-color: #409eff;

            &:hover {
                background: #66b1ff;
                border-color: #66b1ff;
            }
        }

        &.el-button--success {
            background: #67c23a;
            border-color: #67c23a;

            &:hover {
                background: #85ce61;
                border-color: #85ce61;
            }
        }

        &.el-button--warning {
            background: #e6a23c;
            border-color: #e6a23c;

            &:hover {
                background: #ebb563;
                border-color: #ebb563;
            }
        }

        &.el-button--info {
            background: #909399;
            border-color: #909399;

            &:hover {
                background: #a6a9ad;
                border-color: #a6a9ad;
            }
        }
    }

    // 输入框样式优化
    :deep(.el-input) {
        .el-input__inner {
            border-radius: 6px;
            border: 1px solid #dcdfe6;

            &:focus {
                border-color: #409eff;
                box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
        }
    }

    // 响应式设计
    // 大屏幕（>768px）确保水平布局和正确比例
    @media (min-width: 769px) {
        .new-review-package-container {
            .main-content {
                display: flex !important;
                flex-direction: row !important;

                .left-section {
                    flex: 2 !important; // 强制40%宽度
                    width: auto !important;
                    max-width: none !important;
                }

                .right-area {
                    flex: 3 !important; // 强制60%宽度
                    width: auto !important;
                    max-width: none !important;
                }
            }

            // 确保顶部操作区在大屏幕下也保持正确比例
            .header-section {
                display: flex !important;
                flex-direction: row !important;
                align-items: stretch !important;

                .header-left-block {
                    flex: 2 !important; // 强制40%宽度，与主内容区保持一致
                    width: auto !important;
                    max-width: none !important;
                }

                .header-right-block {
                    flex: 3 !important; // 强制60%宽度，与主内容区保持一致
                    width: auto !important;
                    max-width: none !important;
                }
            }
        }
    }

    // 大屏幕到中等屏幕过渡（1201px-1400px）- 调整右区块内部布局
    @media (max-width: 1400px) and (min-width: 1201px) {
        .new-review-package-container {
            .header-section {
                .header-right-block {
                    .type-info-section {
                        min-width: 180px; // 减小最小宽度
                        width: 22%; // 减小占比
                    }

                    .ranking-info-section {
                        min-width: 100px; // 减小最小宽度
                        width: 18%; // 减小占比
                    }
                }
            }
        }
    }

    // 中等屏幕（1001px-1200px）- 进一步压缩固定部分
    @media (max-width: 1200px) and (min-width: 1001px) {
        .new-review-package-container {
            .header-section {
                .header-right-block {
                    .type-info-section {
                        min-width: 160px; // 进一步减小
                        width: 20%;

                        .type-info {
                            .express-type,
                            .order-type {
                                font-size: 14px; // 减小字体

                                .value {
                                    font-size: 18px; // 减小数值字体
                                }
                            }
                        }
                    }

                    .ranking-info-section {
                        min-width: 80px; // 进一步减小
                        width: 15%;
                    }
                }
            }
        }
    }

    // 中等屏幕（769px-1000px）保持水平布局，但调整间距和内边距
    @media (max-width: 1000px) and (min-width: 769px) {
        .new-review-package-container {
            .main-content {
                gap: 16px; // 减小间距
                padding: 2px; // 统一使用2px内边距

                .left-section {
                    padding: 2px; // 统一使用2px内边距
                }

                .right-area {

                    .right-section,
                    .bottom-section {
                        padding: 2px; // 统一使用2px内边距
                    }
                }
            }

            // 在中等屏幕下调整右区块布局
            .header-section {
                .header-right-block {
                    gap: 8px; // 减小间距

                    .type-info-section {
                        min-width: 140px; // 进一步减小
                        width: 25%;

                        .type-info {
                            .express-type,
                            .order-type {
                                font-size: 13px; // 减小字体

                                .label {
                                    min-width: 60px; // 减小标签宽度
                                }

                                .value {
                                    font-size: 16px; // 减小数值字体
                                }
                            }
                        }
                    }

                    .ranking-info-section {
                        display: none; // 在中等屏幕下隐藏复核排名占位
                    }

                    .materials-section {
                        min-width: 250px; // 减小表格最小宽度
                    }
                }
            }
        }
    }

    // 小屏幕过渡（900px-768px）- 隐藏复核排名，优化类型信息
    @media (max-width: 900px) and (min-width: 769px) {
        .new-review-package-container {
            .header-section {
                .header-right-block {
                    gap: 6px; // 进一步减小间距

                    .type-info-section {
                        min-width: 120px; // 最小化宽度
                        width: 30%;

                        .type-info {
                            .express-type,
                            .order-type {
                                font-size: 12px; // 进一步减小字体

                                .label {
                                    min-width: 50px; // 最小化标签宽度
                                }

                                .value {
                                    font-size: 14px; // 最小化数值字体
                                }
                            }
                        }
                    }

                    .ranking-info-section {
                        display: none; // 隐藏复核排名占位
                    }

                    .materials-section {
                        min-width: 200px; // 进一步减小表格最小宽度
                        padding: 0 4px; // 减小内边距
                    }
                }
            }
        }
    }

    // 小屏幕（≤768px）使用垂直布局
    @media (max-width: 768px) and (min-width: 481px) {
        .new-review-package-container {
            .main-content {
                flex-direction: column;
                overflow-y: auto;
                gap: 16px;
                padding: 2px;

                .left-section {
                    width: 100%;
                    margin-bottom: 0;
                    max-height: 50vh; // 限制左侧区域高度
                    padding: 2px;
                }

                .right-area {
                    min-height: 40vh; // 确保右侧区域有足够高度
                }
            }

            .header-section {
                flex-direction: column;
                gap: 2px;
                align-items: stretch;

                .header-left-block,
                .header-right-block {
                    flex: none;
                    width: 100%;

                    .input-group {
                        flex-wrap: wrap;
                        gap: 8px;
                    }

                    .material-code-group {
                        .el-input {
                            width: 200px; // 在中等屏幕上缩小宽度
                        }
                    }
                }

                .header-right-block {
                    flex-direction: column; // 中等屏幕下改为垂直布局
                    gap: 8px;

                    .type-info-section {
                        flex: none; // 不占据弹性空间
                        border-right: none; // 移除右边框
                        border-bottom: 1px solid #e8e8e8; // 添加底边框
                        padding-right: 0;
                        padding-bottom: 8px;
                    }

                    .materials-section {
                        padding: 0; // 移除左右内边距

                        .header-materials-area {
                            max-height: 120px; // 限制整个耗材区域高度

                            .materials-table-container {
                                height: 100px; // 中等屏幕使用较小的固定高度
                                max-height: 100px;
                            }
                        }
                    }

                    .ranking-info-section {
                        flex: none; // 不占据弹性空间
                        border-left: none; // 移除左边框
                        border-top: 1px solid #e8e8e8; // 添加顶边框
                        padding-left: 0;
                        padding-top: 8px;
                        min-height: 60px; // 减小最小高度
                    }
                }
            }
        }
    }



    // 滚动条样式优化
    :deep(*) {

        // 自定义滚动条样式
        &::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;

            &:hover {
                background: #a8a8a8;
            }
        }

        &::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }
    }

    // 极小屏幕（≤480px）使用垂直布局
    @media (max-width: 480px) {
        .new-review-package-container {
            .main-content {
                flex-direction: column !important;
                gap: 12px;
                padding: 2px;

                .left-section,
                .right-area {
                    width: 100% !important;
                    max-width: 100% !important;
                    min-width: auto !important;
                    flex: none !important;
                }

                .left-section {
                    margin-bottom: 0;
                    max-height: 50vh;
                }

                .right-area {
                    min-height: 40vh;
                }
            }

            .header-section {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;

                .header-left-block,
                .header-right-block {
                    flex: none;
                    width: 100%;
                    min-height: auto; // 小屏幕下允许高度自适应

                    .order-info {
                        flex-direction: column;
                        gap: 8px;
                    }
                }

                .header-right-block {
                    flex-direction: column; // 小屏幕下改为垂直布局
                    gap: 8px;

                    .type-info-section {
                        flex: none; // 不占据弹性空间
                        border-right: none; // 移除右边框
                        border-bottom: 1px solid #e8e8e8; // 添加底边框
                        padding-right: 0;
                        padding-bottom: 8px;

                        .type-info {
                            flex-direction: column; // 小屏幕下改为垂直布局
                            gap: 8px;
                            align-items: flex-start;
                        }
                    }

                    .materials-section {
                        padding: 0; // 移除左右内边距

                        .header-materials-area {
                            max-height: 100px; // 小屏幕限制整个耗材区域高度

                            .materials-table-container {
                                height: 80px; // 小屏幕使用更小的固定高度
                                max-height: 80px;
                            }
                        }
                    }

                    .ranking-info-section {
                        flex: none; // 不占据弹性空间
                        border-left: none; // 移除左边框
                        border-top: 1px solid #e8e8e8; // 添加顶边框
                        padding-left: 0;
                        padding-top: 8px;
                        min-height: 50px; // 小屏幕下进一步减小最小高度

                        .ranking-placeholder {
                            min-height: 50px; // 调整占位符高度
                        }
                    }
                }
            }
        }
    }

    // 确保页面不会出现水平滚动条
    html,
    body {
        overflow-x: hidden;
    }
}

// 存储属性和赠品信息样式（与原复核打包页面保持一致）
.warning-class {
    font-size: 36px;
    font-weight: 500;
    color: red;
    margin-top: 8px;
    line-height: 1.2;
}

.gift-class {
    color: #2db7f5;
    font-size: 24px;
    margin-top: 8px;
    line-height: 1.2;
}

// 快捷键按钮样式（与btn-group组件保持一致）
.shortkey-button {
    .button-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        line-height: 1.2;

        .button-label {
            font-size: 12px;
            line-height: 15px;
            height: 15px;
        }

        .button-shortkey {
            font-size: 12px;
            line-height: 15px;
            height: 15px;
            opacity: 0.8;
            margin-top: 1px;
        }
    }

    // 调整按钮高度以适应双行文本
    min-height: 36px;
    padding: 4px 12px;
}
</style>