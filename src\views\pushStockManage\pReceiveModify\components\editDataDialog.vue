<template>
  <div>
    <xyy-dialog key="dialog2" ref="editDataDialog" title="上架单据修改" width="948px">
      <el-form ref="formData" :model="formData" :rules="rules" :label-width="labelWidth" class="clearfix">
        <el-row :gutter="20">
          <el-col :span="6" :md="6">
            <el-form-item label="商品编码" prop="productCode">
              <el-input v-model="formData.productCode" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="18" :md="18">
            <el-form-item label="商品名称" prop="productName">
              <el-input v-model="formData.productName" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24" :md="24">
            <el-form-item label="生产厂家" prop="manufacturer">
              <el-input v-model="formData.manufacturer" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :md="8">
            <el-form-item label="商品规格" prop="specifications">
              <el-input v-model="formData.specifications" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8">
            <el-form-item label="单位" prop="packingUnit">
              <el-input v-model="formData.packingUnit" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8">
            <el-form-item label="包装数量" prop="productPackingBigNumber">
              <el-input v-model="formData.productPackingBigNumber" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6" :md="6">
            <el-form-item label="验收评定" prop="acceptance">
              <el-input v-model="formData.acceptance" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :md="8">
            <el-form-item label="件数" prop="shelvesCountBig">
              <el-input v-model="formData.shelvesCountBig" @input="validateInputBig"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8">
            <el-form-item label="零散数量" prop="shelvesCountScatter">
              <el-input v-model="formData.shelvesCountScatter" @input="validateInputScatter"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8">
            <el-form-item label="数量" prop="shelvesCountSmall">
              <el-input v-model="formData.shelvesCountSmall" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8" :md="8">
            <el-form-item label="批号" prop="productBatchCode">
              <el-input v-model="formData.productBatchCode" @input="validateInput"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8">
            <el-form-item label="重量" prop="weight">
              <el-input v-model="formData.weight" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8">
            <el-form-item label="批准文号" prop="approvalNumbers">
              <el-input v-model="formData.approvalNumbers" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :lg="12" :md="12">
            <el-form-item label="生产日期" prop="productManufactureDate">
              <el-date-picker v-model="formData.productManufactureDate" :picker-options="startDate"
                value-format="yyyy-MM-dd" @change="productManufactureDateComfirm" type="date" />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :md="12">
            <el-form-item label="有效期至" prop="productValidDate">
              <el-date-picker v-model="formData.productValidDate" value-format="yyyy-MM-dd" :picker-options="endDate"
                @change="productValidDateComfirm" type="date" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :lg="24" :md="24">
            <el-form-item label="修改原因" prop="modifyReason">
              <el-select v-model="formData.modifyReason">
                <el-option label="请选择" value="" />
                <el-option v-for="item in modifyReason" :key="item.id" :value="item.dictName" :label="item.dictName" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="danger" @click="close()">
          <svg aria-hidden="true"
            style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
            viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headquxiao1" />
          </svg>
          取消</el-button>
        <el-button type="primary" :autofocus="true" @click="submit()">
          <svg aria-hidden="true"
            style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
            viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headqueren" />
          </svg>
          确定</el-button>
      </span>

    </xyy-dialog>
  </div>
</template>
<script>
import { getWordsLists, groudReceiptUpdate, dateCheck } from '@/api/pushstock/pCheckManage'
import { dateFormat } from "@/utils/index.js"
export default {
  name: 'editDataDialog',
  props: ['formDetail'],
  data() {
    return {
      formData: {
        productCode: '',
        productName: '',
        manufacturer: '',
        packingUnit: '',
        specifications: '',
        productPackingBigNumber: '',
        acceptance: '',
        shelvesCountBig: '',
        shelvesCountScatter: '',
        shelvesCountSmall: '',
        productBatchCode: '',
        weight: '',
        approvalNumbers: '',
        productManufactureDate: '',
        productValidDate: '',
        modifyReason: '',
      },
      rules: {
        shelvesCountBig: [
          {
            required: true,
            message: '请输入件数',
            trigger: 'blur'
          }
        ],
        shelvesCountScatter: [
          {
            required: true,
            message: '请输入零散数',
            trigger: 'blur'
          }
        ],
        productBatchCode: [
          {
            required: true,
            message: '请输入批号',
            trigger: 'blur'
          }
        ],
        productManufactureDate: [
          {
            required: true,
            message: '请输入生产日期',
            trigger: 'blur'
          }
        ],
        productValidDate: [
          {
            required: true,
            message: '请输入有效期至',
            trigger: 'blur'
          }
        ],
        modifyReason: [
          {
            required: true,
            message: '请选择修改原因',
            trigger: 'change'
          }
        ]
      },
      startDate: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      endDate: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      labelWidth: '110px',
      modifyReason: [],
      // 入参数据
      notDetailData: {},
      // 计算后的总数
      newTotalData: 0,
      // 判断修改后的数量是否合规
      whetherNewTotal: 0
    }
  },
  // 监视数据有没有改变
  watch: {
    formData: {
      handler(newValue, oldValue) {
        this.getTotalData()
      },
      deep: true
    }
  },
  methods: {
    // 验收评定计算
    conputeAcceptance() {
      if (this.formData.storageTypeCode == 'THK') {
        this.formData.acceptance = '不合格'
      } else {
        this.formData.acceptance = '合格'
      }
    },
    // 数量计算
    getTotalData() {
      this.newTotalData = Number(this.formData.shelvesCountBig) * Number(this.formData.productPackingBigNumber) + Number(this.formData.shelvesCountScatter)
      this.formData.shelvesCountSmall = this.newTotalData
    },
    // 获取字典
    getWords() {
      const dictType = 'SJCWXGYY'
      const params = Object.assign({
        dictType: dictType
      })
      getWordsLists(params).then(res => {
        const { code, result } = res
        if (code === 0) {
          this.modifyReason = result
        }
        else {
          this.modifyReason = []
        }
      })
    },
    // 关闭对话框执行的回调
    close() {
      this.formData = {
        productCode: '',
        productName: '',
        manufacturer: '',
        packingUnit: '',
        specifications: '',
        productPackingBigNumber: '',
        acceptance: '',
        shelvesCountBig: '',
        shelvesCountScatter: '',
        shelvesCountSmall: '',
        productBatchCode: '',
        weight: '',
        approvalNumbers: '',
        productManufactureDate: '',
        productValidDate: '',
        modifyReason: '',
      }
      this.$refs.formData.resetFields()
      document.removeEventListener('keydown', this.handleKeyDown) // 移除快捷键
      this.$refs.editDataDialog.close()
      this.$emit('on-before-close')
    },
    // 检测批号输入是否合规
    validateInput() {
      // const regex = /^[0-9a-zA-Z-]+$/
      // console.log('-----');
      // if (!this.formData.productBatchCode.match(regex)) {
      //   this.formData.productBatchCode = this.formData.productBatchCode.replace(/[^0-9a-zA-Z-]/g, '')
      // }
      return true
    },
    // 校验件数输入是否合规
    validateInputBig() {
      const regex = /^[0-9]+$/
      if (!regex.test(this.formData.shelvesCountBig)) {
        this.formData.shelvesCountBig = this.formData.shelvesCountBig.replace(/\D/g, '')
      }
    },
    // 检测零散数输入是是否合规
    validateInputScatter() {
      const regex = /^[0-9]+$/
      if (!regex.test(this.formData.shelvesCountScatter)) {
        this.formData.shelvesCountScatter = this.formData.shelvesCountScatter.replace(/\D/g, '')
      }
    },
    handleClose() {
      this.$emit('on-before-close')
    },
    open(detailData, data) {
      this.formData = {
        productCode: '',
        productName: '',
        manufacturer: '',
        packingUnit: '',
        specifications: '',
        productPackingBigNumber: '',
        acceptance: '',
        shelvesCountBig: '',
        shelvesCountScatter: '',
        shelvesCountSmall: '',
        productBatchCode: '',
        weight: '',
        approvalNumbers: '',
        productManufactureDate: '',
        productValidDate: '',
        modifyReason: '',
      }
      this.$refs.editDataDialog.open()
      this.formData = JSON.parse(JSON.stringify(this.formDetail))
      this.conputeAcceptance()
      this.notDetailData = data
      this.getWords()
    },
    // 确定之后进行提交
    submit() {
      // 兜底校验
      this.$refs.formData.validate(valid => {
        if (valid) {
          if (this.formData.shelvesCountBig == this.formDetail.shelvesCountBig &&
            this.formData.shelvesCountScatter == this.formDetail.shelvesCountScatter &&
            this.formData.productBatchCode == this.formDetail.productBatchCode &&
            this.formData.productManufactureDate == this.formDetail.productManufactureDate &&
            this.formData.productValidDate == this.formDetail.productValidDate &&
            this.formData.shelvesCountSmall == this.formDetail.shelvesCountSmall
          ) {
            this.$message.error('数量、批号、生产日期、有效期至并未修改')
          }
          else {
            this.getTotalData()
            if (Number(this.newTotalData) > Number(this.formDetail.shelvesCountSmall) || Number(this.formData.shelvesCountScatter) > Number(this.formDetail.shelvesCountScatter) || Number(this.formData.shelvesCountBig) > Number(this.formDetail.shelvesCountBig)) {
              this.openNumberErr()
            } else {
              // 入参
              const dateData = {
                productManufactureDate: this.formData.productManufactureDate,
                productValidDate: this.formData.productValidDate
              }
              // 校验日期是否符合规范
              dateCheck(dateData).then(res => {
                const { code, result, msg } = res
                if (code === 0) {
                  if (result == 1) {
                    this.openForbid()
                  } else if (result == 2) {
                    this.openSubmit()
                  } else {
                    this.submitFinall()
                  }
                } else {
                  this.$message.error(msg)
                }
              })
            }
          }
        } else {
          this.$message.error('输入不能为空！')
        }
      })
    },
    productManufactureDateComfirm() {
      if (this.formData.productManufactureDate > this.formData.productValidDate) {
        this.$message.warning('生产日期不能超过有效期至')
        this.formData.productManufactureDate = this.formDetail.productManufactureDate
        return
      }
    },
    productValidDateComfirm() {
      if (this.formData.productManufactureDate > this.formData.productValidDate) {
        this.$message.warning('有效期至不能早于生产日期')
        this.formData.productValidDate = this.formDetail.productValidDate
        return
      }
    },
    // 进行数据提交
    submitFinall() {
      const filterNotDetailObj = {
        id: this.notDetailData.id,
        storageOrderCode: this.notDetailData.storageOrderCode,
        arrivalDate: this.notDetailData.arrivalDate,
        supplierCode: this.notDetailData.supplierCode,
        supplierName: this.notDetailData.supplierName,
        purchaseUsers: this.notDetailData.purchaseUsers,
        receiveUserName: this.notDetailData.receiveUserName,
        checkUserName: this.notDetailData.checkUserName,
        recheckUserName: this.notDetailData.recheckUserName,
        headRecheckUserName: this.notDetailData.headRecheckUserName,
        channelNames: this.notDetailData.channelNames,
        buildingName: this.notDetailData.buildingName,
        ownerName: this.notDetailData.ownerName,
        warehouseName: this.notDetailData.warehouseName,
        storageOrderStatus: this.notDetailData.storageOrderStatus
      }
      this.formData = Object.assign(this.formData, {
        productManufactureDate: dateFormat(this.formData.productManufactureDate, 'yyyy-MM-dd'),
        productValidDate: dateFormat(this.formData.productValidDate, 'yyyy-MM-dd')
      })
      const params = Object.assign(filterNotDetailObj, { purchaseStorageOrderDetailVoList: [this.formData] })
      groudReceiptUpdate(params).then(res => {
        const { result, code, msg } = res
        if (code === 0) {
          this.openUpdateSuccess()
        } else {
          this.close()
          this.$message.error(msg)
        }
      })
    },
    // 弹框提示
    openSubmit() {
      this.$confirm('确认近效期商品入库?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitFinall()
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    // 禁售效期商品禁止入库
    openForbid() {
      this.close()
      this.$alert('禁售效期商品禁止入库！', '提示', {
        confirmButtonText: 'ok',
        callback: action => {
        }
      });
    },
    // 修改成功的提示
    openUpdateSuccess() {
      this.close()
      this.$alert('修改单据成功！', '提示', {
        confirmButtonText: 'ok',
        callback: action => {
        }
      });
    },
    // 数量修改不正确的提示
    openNumberErr() {
      this.$alert('数量的修改值不正确！', '提示', {
        confirmButtonText: 'ok',
        callback: action => {
        }
      });
    },
  }
}
</script>