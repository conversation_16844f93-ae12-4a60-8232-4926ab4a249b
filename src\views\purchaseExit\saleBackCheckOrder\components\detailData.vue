<template>
  <div>
    <xyy-panel title="录入验收结果">
      <btn-group slot="tools" :btn-list="btnList" />
      <el-form
        ref="formData"
        :model="formData"
        label-width="120px"
        class="clearfix"
      >
        <el-col :lg="6" :md="6">
          <el-form-item label="销售退回验收单号:">{{
            formData.checkCode
          }}</el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="单据日期:">{{
            formData.documentTime
          }}</el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="单据状态:">{{
            formData.status == "0" ? "待验收" : ""
          }}</el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="销退收货单号:">{{
            formData.receiveCode
          }}</el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="退货单位:">{{
            formData.returnOrganization
          }}</el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="收货员:">{{ formData.receiver }}</el-form-item>
        </el-col>
        <el-col :lg="8" :md="8">
          <el-form-item label="销售订单号:">{{
            formData.orderCode
          }}</el-form-item>
        </el-col>
        <el-col :lg="4" :md="4">
          <el-form-item label="验收员:">{{
            formData.checkPerson
          }}</el-form-item>
        </el-col>
        <el-col :lg="6" :md="6">
          <el-form-item label="验收日期:">{{
            formData.checkTime
          }}</el-form-item>
        </el-col>
        <el-col :lg="6" :md="6" v-if="isShowYan">
          <el-form-item label="验收员2:">
            <el-input
              v-model="formData.checkPersonOther"
              @focus="focusCheck"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-form>
    </xyy-panel>

    <xyy-panel title="商品明细">
      <div v-table class="table-box">
        <vxe-table
          ref="xTable"
          :loading="loading"
          highlight-current-row
          height="auto"
          :data="tableData"
          @cell-dblclick="openSplitDialog"
          row-id="id"
        >
          <vxe-table-column type="seq" title="序号" width="80" />
          <vxe-table-column title="验收件数" field="realPiecesNum" width="160" fixed="left">
            <template #default="{ row, rowIndex }">
              <el-input
                  v-model.number="row.realPiecesNum"
                  :disabled="row.realScatteredNum > 0 || Boolean(row.productId)"
                  placeholder="0"
                  @input="changeNum(row)"
                ></el-input>
            </template>
          </vxe-table-column>
          <vxe-table-column title="验收零散数" field="realScatteredNum" width="160" fixed="left">
            <template #default="{ row, rowIndex }">
              <el-input
                  v-model.number="row.realScatteredNum"
                  :disabled="row.realPiecesNum > 0 || Boolean(row.productId)"
                  placeholder="0"
                  @input="changeNum(row)"
                ></el-input>
            </template>
          </vxe-table-column>
          <vxe-table-column title="容器编号" field="containerCode" width="160" fixed="left">
            <template #default="{ row, rowIndex }">
              <el-input v-model="row.containerCode">
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="openContainerList(row, rowIndex)"
                  />
                </el-input>
            </template>
          </vxe-table-column>
          <vxe-table-column
            v-for="item in productColumns"
            :key="item.field"
            :field="item.field"
            :title="item.title"
            :width="item.width"
          >
            <template #default="{ row, rowIndex }">
              <span v-if="item.field === 'storageClassification'">
                {{ row.storageClassification === "0" ? "整散分开" : "整散合一" }}
              </span>
              <span v-else-if="item.field === 'checkAssess'">
                <el-select
                  v-model="row.checkAssess"
                  @change="changeReCheck(row)"
                >
                  <el-option
                    v-for="item in optResult"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  />
                </el-select>
              </span>
              <!-- 采取措施 -->
              <span v-else-if="item.field === 'measures'">
                <el-select
                  v-model="row.measures"
                  :disabled="row.checkAssess == 3"
                  v-if="row.checkAssess == 1"
                >
                  <el-option
                    v-for="item in measuresSelect1"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-select
                  v-model="row.measures"
                  :disabled="row.checkAssess == 3"
                  v-else-if="row.checkAssess == 2"
                >
                  <el-option
                    v-for="item in measuresSelect2"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <el-select
                  v-model="row.measures"
                  :disabled="row.checkAssess == 3"
                  v-else-if="row.checkAssess == 3"
                >
                  <el-option
                    v-for="item in measuresSelect3"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </span>
              <!-- 不合格事项 -->
              <span v-else-if="item.field === 'unqualifiedMatters'">
                <el-select
                  v-model="row.unqualifiedMatters"
                  :disabled="row.checkAssess != 2"
                  v-if="row.checkAssess == 1 || row.checkAssess == 3"
                >
                  <el-option
                    v-for="item in unqualifiedMattersSelect1"
                    :key="item.dictCode"
                    :label="item.dictName"
                    :value="item.dictCode"
                  />
                </el-select>
                <el-select
                  v-model="row.unqualifiedMatters"
                  :disabled="row.checkAssess != 2"
                  v-if="row.checkAssess == 2"
                >
                  <el-option
                    v-for="item in unqualifiedMattersSelect2"
                    :key="item.dictCode"
                    :label="item.dictName"
                    :value="item.dictCode"
                  />
                </el-select>
              </span>
              <!-- <span v-else-if="item.field === 'realPiecesNum'">
                <el-input
                  v-model.number="row.realPiecesNum"
                  :disabled="row.realScatteredNum > 0 || Boolean(row.productId)"
                  placeholder="0"
                  @input="changeNum(row)"
                ></el-input>
              </span> -->
              <!-- <span v-else-if="item.field === 'realScatteredNum'">
                <el-input
                  v-model.number="row.realScatteredNum"
                  :disabled="row.realPiecesNum > 0 || Boolean(row.productId)"
                  placeholder="0"
                  @input="changeNum(row)"
                ></el-input>
              </span> -->
              <span v-else-if="item.field === 'returnNum'">
                {{
                  row.realPiecesNum && !row.realScatteredNum
                    ? row.realPiecesNum * row.packageNum
                      ? row.realPiecesNum * row.packageNum
                      : "0"
                    : row.realScatteredNum
                    ? row.realScatteredNum
                    : "0"
                }}
              </span>
              <span v-else-if="item.field === 'sampleNum'">
                {{
                  row.realPiecesNum && !row.realScatteredNum
                    ? row.realPiecesNum <= 6
                      ? Math.floor(6 * row.realPiecesNum)
                      : row.realPiecesNum > 6 && row.realPiecesNum <= 50
                      ? 36
                      : row.realPiecesNum % 50 == 0
                      ? Math.floor((6 + (row.realPiecesNum - 50) / 25) * 6)
                      : Math.floor((6 + row.realPiecesNum / 25) * 6)
                      ? row.realPiecesNum <= 6
                        ? Math.floor(6 * row.realPiecesNum)
                        : row.realPiecesNum > 6 && row.realPiecesNum <= 50
                        ? 36
                        : row.realPiecesNum % 50 == 0
                        ? Math.floor((6 + (row.realPiecesNum - 50) / 25) * 6)
                        : Math.floor((6 + row.realPiecesNum / 25) * 6)
                      : "0"
                    : row.realScatteredNum
                    ? row.realScatteredNum
                    : "0"
                }}
              </span>
              <!-- <span v-else-if="item.field === 'containerCode'">
                <el-input v-model="row.containerCode">
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="openContainerList(row, rowIndex)"
                  />
                </el-input>
              </span> -->
              <span v-else>{{ row[item.field] }}</span>
            </template>
          </vxe-table-column>
        </vxe-table>
    </div>
    </xyy-panel>

    <!-- <div class="pager">
      <vxe-pager
        border
        :current-page="tablePage.pageNum"
        :page-size="tablePage.pageSize"
        :total="tablePage.total"
        :layouts="[
          'PrevPage',
          'JumpNumber',
          'NextPage',
          'FullJump',
          'Sizes',
          'Total',
        ]"
        @page-change="handlePageChange"
      />
    </div> -->

    <el-dialog title="验收员2" :visible.sync="isShow">
      <el-form :model="form">
        <el-form-item label="账号" :label-width="formLabelWidth">
          <el-input v-model="form.username" autocomplete="off"></el-input>
        </el-form-item>
        <el-form-item label="密码" :label-width="formLabelWidth">
          <el-input
            v-model="form.password"
            type="password"
            autocomplete="off"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">取 消</el-button>
        <el-button type="primary" @click="confirmUser">确 定</el-button>
      </div>
    </el-dialog>
    <dialogContainer
      ref="dialogContainer"
      @on-close="onContainerClose"
    ></dialogContainer>
    <split-form-row ref="splitFormRow" @sendData="receiveData"></split-form-row>
    <tableDialog
      ref="tableDialog"
      @confirmName="confirmName"
      @cancelClose="cancelClose"
    ></tableDialog>
  </div>
</template>

<script>
import utils from '@/utils'
import dialogContainer from "./dialogContainer.vue";
import { productColumns } from "../config";
import splitFormRow from "./splitFormRow.vue";
import tableDialog from "./tableDialog.vue";
import { getDisqualification } from "@/api/salesReturn/saleBackReview.js";
import {
  findCheckByCheckCode,
  findCheckDetails,
  confirmCheck,
  checkPermissionBystaffNum,
} from "../../../../api/purchaseExit/saleBackCheckOrder";
import { exportData } from "../../../../api/public";
export default {
  name: "detailData",
  components: { dialogContainer, splitFormRow, tableDialog },
  data() {
    return {
      isShowYan: false,
      isShow: false,
      formLabelWidth: "80px",
      btnList: [
        {
          label: "导出",
          type: "success",
          icon: "el-icon-download",
          code: "btn:wms:saleBackCheckOrderDetail:export",
          clickEvent: this.exportOrder,
        },
        {
          label: "返回",
          type: "warning",
          icon: "el-icon-back",
          code: "btn:wms:saleBackCheckOrderDetail:back",
          clickEvent: this.goToBack,
        },
        {
          label: "确认验收",
          type: "primary",
          icon: "el-icon-check",
          code: "btn:wms:saleBackCheckOrderDetail:confirm",
          clickEvent: this.confirmCheck,
        },
      ],
      formData: {
        checkCode: "", //销售退回验收单号
        documentTime: "", //单据日期
        status: "", //单据状态
        receiveCode: "", //销退收货单号
        returnOrganization: "", //退货单位
        receiver: "", //收货员
        orderCode: "", //销售订单号
        checkPerson: "", //验收员
        updateTime: "", //验收日期
        checkPersonOther: "", //验收员2
      },
      loading: false,
      tableData: [],
      oaId: "",
      productColumns: productColumns(),
      //分页参数
      tablePage: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
        pageSizes: [
          {
            label: "10条/页",
            value: 10,
          },
          {
            label: "20条/页",
            value: 20,
          },
          {
            label: "50条/页",
            value: 50,
          },
          {
            label: "100条/页",
            value: 100,
          },
        ],
      },
      //验收评定下拉框
      optResult: [
        {
          value: 1,
          label: "合格",
        },
        {
          value: 2,
          label: "不合格",
        },
        {
          value: 3,
          label: "待处理",
        },
      ],
      // 采取措施
      measuresSelect1: [
        {
          value: "1",
          label: "入合格库",
        },
        // {
        //   value: "5",
        //   label: "入特价库",
        // },
      ],
      measuresSelect2: [
        {
          value: "3",
          label: "入不合格库",
        },
        {
          value: "2",
          label: "入退货区",
        },
      ],
      measuresSelect3: [],
      // 不合格事项
      unqualifiedMattersSelect1: [],
      unqualifiedMattersSelect2: [],
      // 初始值
      oldTableData: [],
      form: {
        username: "",
        password: "",
      },
    };
  },
  // mounted() {
  //   this.formInfo = this.$route.query.formInfo;
  //   this.findCheckByCheckCode();
  //   this.findCheckDetails();
  //   this.getDisqualificationLists();
  //   this.oaId = this.storageTool.getUserInfo().oaId;
  // },
  activated() {
    this.$nextTick(()=>{
      utils.pageActivated()
    })
    this.formInfo = this.$route.query.formInfo;
    this.findCheckByCheckCode();
    this.findCheckDetails();
    this.getDisqualificationLists();
  },
  methods: {
    exportOrder(){
      if(this.tableData.length == 0){
        this.$message.warning('暂无数据导出')
        return
      }
      const formInfos = {
        checkCode: this.formInfo,
        // checkCode: "XTYS2407300000012",
        pageNum: 1,
        pageSize: 1000,
      };
      const columns = JSON.parse(JSON.stringify(this.productColumns));
      columns.forEach((item) => {
        if(item.field == 'checkAssess'){
          item.field = 'checkAssessDesc'
        }
        if(item.field == 'measures'){
          item.field = 'measuresName'
        }
      })
      const colNameDesc = columns.map(item => item.title).join(',')
      const colName = columns.map(item => item.field).join(',')
      const exportParams = JSON.stringify(formInfos)
      const params = {
                moduleName: 'INSTOCK',
                menuDesc: '销售退回-销售退回验收单明细',
                taskBean: 'INSTOCK_SALESRETURN_findCheckDetailPageByCheckCode',
                orgCode: this.storageTool.getUserInfo().warehouse.orgCode,
                warehouseCode: this.storageTool.getUserInfo().warehouse.warehouseCode,
                colNameDesc: colNameDesc,
                colName: colName,
                exportParams: exportParams
            }
            // console.log(params, 'params');
            this.$confirm('是否确认导出表单内容？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                exportData(params).then(res => {
                    const { code, msg, result } = res
                    if (code === 0 && result) {
                        this.$message({
                            type: 'success',
                            message: '导出成功，请前往下载中心查看！！!'
                        })
                    } else {
                        this.$message.error(msg)
                    }
                })
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消导出'
                });
            });
    },
    //查询录入收货结果
    async findCheckByCheckCode() {
      // console.log(this.formInfo);
      const res = await findCheckByCheckCode({
        checkCode: this.formInfo,
        // receiveCode: "XTSH2407190000017",
      });
      console.log(res);
      if (res.code === 0) {
        this.formData = res.result;
      } else {
        this.$message.error(res.msg);
      }
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.pageNo = currentPage;
      this.tablePage.pageSize = pageSize;
      this.findCheckDetails();
    },
    //查询表数据
    async findCheckDetails() {
      this.loading = true;
      const params = {
        checkCode: this.formInfo,
        // checkCode: "XTYS2407300000012",
        pageNum: 1,
        pageSize: 1000,
      };
      const res = await findCheckDetails(params);
      console.log(res);
      if (res.code != 0) {
        this.$message.error(res.msg);
        this.loading = false;
        this.tableData = [];
      } else {
        this.tableData = res.result.list;
        this.oldTableData = JSON.parse(JSON.stringify(res.result.list));
        if (
          this.tableData[0].productLargeClass == 5 ||
          this.tableData[0].productLargeClass == 6
        ) {
          this.isShowYan = true;
        } else {
          this.isShowYan = false;
        }
        // this.isShowYan = true;
        this.tableData.forEach((item) => {
          if (item.currentMonths > 0) {
            item.checkAssess = 1;
            item.measures = "1";
          } else {
            item.checkAssess = 2;
            item.measures = "3";
            this.unqualifiedMattersSelect2 = [
              {
                dictCode: "",
                dictName: "",
              },
            ];
            item.unqualifiedMatters = "";
          }
          if (item.unqualifiedMatters == 0) {
            item.unqualifiedMatters = "";
          }
        });
        this.tablePage.total = res.result.total;
        this.tablePage.pageNo = res.result.packageNum;
        this.loading = false;
        console.log(this.tableData);
      }
    },
    // 切换验收评定执行的回调
    changeReCheck(row) {
      // 合格
      this.checkAssess = row.checkAssess;
      if (row.checkAssess == 1) {
        this.measuresSelect1 = [
          {
            value: "1",
            label: "入合格库",
          },
          // {
          //   value: "5",
          //   label: "入特价库",
          // },
        ];
        row.measures = "1";
        this.unqualifiedMattersSelect1 = [
          {
            dictCode: "",
            dictName: "",
          },
        ];
        row.unqualifiedMatters = "";
        // 容器编号为初始值
        this.oldTableData.forEach((oldRow) => {
          if (row.id === oldRow.id) {
            row.containerCode = oldRow.containerCode;
          }
        });
      } else if (row.checkAssess == "2") {
        row.measures = "2";
        this.measuresSelect2 = [
          {
            value: "3",
            label: "入不合格库",
          },
          {
            value: "2",
            label: "入退货区",
          },
        ];
        row.unqualifiedMatters = "采购没有要货";
        // 清空容器编号
        row.containerCode = "";
      } else if (row.checkAssess == "3") {
        row.measures = "4";
        this.measuresSelect3 = [
          {
            value: "4",
            label: "移入待处理区，等待复查",
          },
        ];
        this.unqualifiedMattersSelect1 = [
          {
            dictCode: "",
            dictName: "",
          },
        ];
        row.unqualifiedMatters = "";
        // 清空容器编号
        row.containerCode = "";
      }
    },
    // 复核之后的初始化
    reviewChangeCheck(row) {
      // 合格
      if (row.checkAssess == 1) {
        this.measuresSelect1 = [
          {
            value: "1",
            label: "入合格库",
          },
          // {
          //   value: "5",
          //   label: "入特价库",
          // },
        ];
        this.unqualifiedMattersSelect1 = [
          {
            dictCode: "",
            dictName: "",
          },
        ];
        row.unqualifiedMatters = "";
        // 容器编号为初始值
        // this.oldTableData.forEach((oldRow) => {
        //   if (row.id === oldRow.id) {
        //     row.containerCode = oldRow.containerCode;
        //   }
        // });
      } else if (row.checkAssess == "2") {
        this.measuresSelect2 = [
          {
            value: "3",
            label: "入不合格库",
          },
          {
            value: "2",
            label: "入退货区",
          },
        ];
        row.unqualifiedMatters = "采购没有要货";
        // 清空容器编号
        // row.containerCode = "";
      } else if (row.checkAssess == "3") {
        this.measuresSelect3 = [
          {
            value: "4",
            label: "移入待处理区，等待复查",
          },
        ];
        this.unqualifiedMattersSelect1 = [
          {
            dictCode: "",
            dictName: "",
          },
        ];
        row.unqualifiedMatters = "";
        // 清空容器编号
        // row.containerCode = "";
      }
    },
    // 获取不合格品的信息
    getDisqualificationLists() {
      getDisqualification({ dictType: "jsyy" }).then((res) => {
        const { code, msg, result } = res;
        if (code === 0) {
          this.unqualifiedMattersSelect2 = result || [];
        } else {
          this.$message.error(msg);
        }
      });
    },
    //打开容器编号
    openContainerList(row, rowIndex) {
      // console.log(rowIndex);
      if (!row.realPiecesNum && !row.realScatteredNum) {
        this.$alert("实际件数和实际零散数不能同时为0!", "错误信息", {
          confirmButtonText: "我知道了",
        });
        return;
      }
      this.$refs.dialogContainer.open(row, rowIndex);
    },
    // 容器弹窗选择关闭
    onContainerClose({ row, rowIndex }) {
      // console.log(row,rowIndex);
      // this.tableData[rowIndex].containerCode = row.containerCode;
      // console.log(this.tableData);
      this.tableData[rowIndex].containerCode =
        row.data[row.$rowIndex].containerCode;
    },
    // 打开拆分表单行弹框
    openSplitDialog(val) {
      let sameIdData = this.tableData.filter((data) => data.id == val.row.id);
      sameIdData.forEach((item) => {
        item.returnNum =
          item.realPiecesNum && !item.realScatteredNum
            ? item.realPiecesNum * item.packageNum
              ? item.realPiecesNum * item.packageNum
              : "0"
            : item.realScatteredNum
            ? item.realScatteredNum
            : "0";
      });
      console.log(sameIdData);

      this.$refs.splitFormRow.open(sameIdData);
    },
    // 接收拆行数据
    receiveData(splitDataTable) {
      // 过滤拿到拆分后的数据
      let firstIdOfSplit = splitDataTable[0].id;
      this.tableData = this.tableData.filter(
        (item) => item.id !== firstIdOfSplit
      );
      this.tableData.push(...splitDataTable);
      this.tableData.forEach((item) => {
        this.reviewChangeCheck(item);
      });
    },
    //确认验收
    async confirmCheck() {
      const containerList = this.tableData.filter((item) => {
        return !item.containerCode;
      });
      if (containerList.length > 0) {
        this.$message("请选择容器编号!");
        return;
      }
      let alertData = this.tableData.filter((item) => {
        return item.ranReceveMonths >= item.currentMonths;
      });
      let arr = [];
      // console.log(alertData);

      alertData.forEach((item) => {
        if (item.checkAssess === 1) {
          arr.push(this.tableData.indexOf(item) + 1);
        }
      });
      // console.log(arr);
      if (arr.length != 0) {
        this.$confirm(`第${arr.join()}行为近效期商品,是否继续提交`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.yanshou();
            return;
          })
          .catch(() => {
            return;
          });
      } else {
        this.yanshou();
      }
    },
    async yanshou() {
      // let num = 0;
      // let arr = this.tableData.filter((item) => {
      //   num =
      //     item.realPiecesNum && !item.realScatteredNum
      //       ? item.realPiecesNum * item.packageNum
      //         ? item.realPiecesNum * item.packageNum
      //         : "0"
      //       : item.realScatteredNum
      //       ? item.realScatteredNum
      //       : "0";
      //   return item.realReturnNum != num;
      // });
      // console.log(arr);
      // if (arr.length != 0) {
      //   this.$message.error("实际退回数量不等于验收数量!");
      //   return;
      // }

      let arr = [];
      this.tableData.forEach((item) => {
        arr.push(item.id);
      });
      let idList = [];
      let isAlert = false;
      arr.forEach((item) => {
        if (!idList.includes(item)) {
          idList.push(item);
        }
      });
      // console.log(idList);
      let compare = [];
      let compareList = [];
      idList.forEach((id) => {
        let total = 0;
        let totalTwo = 0;
        this.tableData.forEach((item) => {
          if (id === item.id) {
            total =
              +(item.realPiecesNum && !item.realScatteredNum
                ? item.realPiecesNum * item.packageNum
                  ? item.realPiecesNum * item.packageNum
                  : "0"
                : item.realScatteredNum
                ? item.realScatteredNum
                : "0") + total;
            totalTwo = item.realReturnNum + totalTwo;
          }
        });
        compare = this.tableData.filter((item) => item.id === id);
        console.log(total, compare);
        if (total != totalTwo) {
          compareList.push(compare[0]);
          this.showConfirm(compare);
          isAlert = true;
          return;
        }
      });

      // for (let i = 0; i <= idList.length; i++) {
      //   let total = 0;
      //   let totalTwo = 0;
      //   this.tableData.forEach((item) => {
      //     if (idList[i] === item.id) {
      //       total =
      //         +(item.realPiecesNum && !item.realScatteredNum
      //           ? item.realPiecesNum * item.packageNum
      //             ? item.realPiecesNum * item.packageNum
      //             : "0"
      //           : item.realScatteredNum
      //           ? item.realScatteredNum
      //           : "0") + total;
      //       totalTwo = item.realReturnNum + totalTwo;
      //     }
      //   });
      //   compare = this.tableData.filter((item) => item.id === idList[i]);
      //   console.log(total, compare);
      //   if (total != totalTwo) {
      //     compareList.push(compare[0]);
      //     this.showConfirm(compare);
      //     isAlert = true;
      //     return;
      //   }
      // }

      this.tableData.forEach((item) => {
        delete item.productId;
        delete item.colName;
        delete item.colNameValue;
        delete item.uuid;
      });
      let dataList = [];
      this.tableData.forEach((item) => {
        const data = {
          batchNum: item.batchNum,
          channelName: item.channelCode,
          checkAssess: item.checkAssess,
          containerCode: item.containerCode,
          id: item.id,
          measures: item.measures,
          ownerName: item.ownerName,
          productCode: item.productCode,
          productLargeClass: item.productLargeClass,
          lineReturnNum:
            item.realPiecesNum && !item.realScatteredNum
              ? item.realPiecesNum * item.packageNum
                ? item.realPiecesNum * item.packageNum
                : "0"
              : item.realScatteredNum
              ? item.realScatteredNum
              : "0",
          realPiecesNum: item.realPiecesNum,
          realReturnNum: item.returnNum,
          realScatteredNum: item.realScatteredNum,
          sampleNum: item.sampleNum,
          storageClassification: item.storageClassification,
          unqualifiedMatters: item.unqualifiedMatters,
          buildingName: "",
          warehouseCode: item.warehouseCode,
        };
        dataList.push(data);
      });
      const params = {
        checkCode: this.formData.checkCode,
        checkPersonOther: this.formData.checkPersonOther,
        checkTime: this.formData.checkTime,
        checkerIdOther: this.formData.checkerIdOther,
        list: dataList,
      };
      console.log(params);
      const res = await confirmCheck(params);
      if (res.code === 0) {
        this.findCheckDetails();
        this.$message.success("验收成功!");
        // 保存成功后 重新请求当前页面
        this.$store.state.tagsView.visitedViews =
          this.$store.state.tagsView.visitedViews.filter(
            (item) => item.name !== "saleBackCheckOrderDetail"
          );
        this.$router.replace({
          path: "/purchaseExit/saleBackCheckOrder",
        });
      } else {
        this.$message.error(res.msg);
      }
    },
    async showConfirm(compare) {
      setTimeout((items) => {
        this.$alert(
          `名称为【${compare[0].productName}】,批号为【${compare[0].batchNum}】实际退回数量不等于退回总数量!`,
          "错误信息",
          {
            confirmButtonText: "我知道了",
          }
        );
      });
    },
    //返回
    goToBack() {
      this.$confirm("返回将失去当前页面录入信息，是否继续?", "提示信息", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          //返回
          setTimeout(() => {
            // 保存成功后 重新请求当前页面
            this.$store.state.tagsView.visitedViews =
              this.$store.state.tagsView.visitedViews.filter(
                (item) => item.name !== "saleBackCheckOrderDetail"
              );
            this.$router.replace({
              path: "/purchaseExit/saleBackCheckOrder",
            });
          }, 500);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消",
          });
        });
    },
    //验收员2
    focusCheck() {
      console.log(11);
      this.isShow = true;
    },
    async confirmUser() {
      const loginUsername = this.storageTool.getUserInfo().account
      if(loginUsername == this.form.username){
        this.$message.warning("登录账号不能是同一人");
        return
      }
      const params = {
        staffNum: this.form.username,
        password: this.form.password,
        resourceCode: "salesreturnDoubleCheck",
      };
      const res = await checkPermissionBystaffNum(params);
      // this.isShow = false;
      if (res.code != 0) {
        this.$message.error(res.msg);
        return;
      }

      if (res.result.result.oaId === this.oaId) {
        this.$message.error("登陆账号不能是同一人!");
        return;
      }
      this.res = res;
      this.$refs.tableDialog.open(this.tableData);
      // this.formData.checkPersonOther = res.result.result.realname;
      // this.formData.checkerIdOther = res.result.result.oaId;
      // console.log(this.formData);
      // this.isShow = false;
    },
    confirmName() {
      this.formData.checkPersonOther = this.res.result.result.realname;
      this.formData.checkerIdOther = this.res.result.result.oaId;
      console.log(this.formData);
      this.isShow = false;
      this.form.username = "";
      this.form.password = "";
    },
    cancelClose() {
      this.isShow = false;
      this.form.username = "";
      this.form.password = "";
    },
    changeNum(row) {
      row.containerCode = "";
    },
  },
};
</script>

<style></style>
