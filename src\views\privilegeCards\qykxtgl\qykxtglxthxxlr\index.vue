<template>
    <div class="app-container">
        <xyy-panel title="退货信息录入">
            <el-form ref="formData" :model="formData" label-width="120px" :rules="formDataRules" class="demo-ruleForm">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item prop="provinceName">
                            <template slot="label">
                                <span>寄件地址</span>
                                <span style="margin-left:5px;">省</span>
                            </template>
                            <el-select v-model="formData.provinceName" @change="changeAreaOfCity">
                                <el-option v-for="item in provinData" :label="item.name" :value="item.name"
                                    :key="item.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="市" prop="cityName">
                            <el-select v-model="formData.cityName" @change="changeAreaOfDistrict">
                                <el-option v-for="item in cityData" :label="item.name" :value="item.name"
                                    :key="item.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="区/县" prop="regionalName">
                            <el-select v-model="formData.regionalName" @change="changeAreaOfRegionalName">
                                <el-option v-for="item in districtData" :label="item.name" :value="item.name"
                                    :key="item.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="详细地址" prop="detailedAddress">
                            <el-input v-model="formData.detailedAddress" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="快递公司" prop="deliveryCompanies">
                            <el-select v-model="formData.deliveryCompanies" placeholder="请选择">
                                <el-option v-for="item in deliveryCompaniesOptions" :key="item.value"
                                    :label="item.label" :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="快递单号" prop="logisticsNo">
                            <el-input v-model="formData.logisticsNo" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="包裹数" prop="packageNum">
                            <el-input :value="formData.packageNum" placeholder="" @input="packageNumInput"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="联系人" prop="contacts">
                            <el-input v-model="formData.contacts" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="联系电话" prop="contactsPhoneNum">
                            <el-input v-model="formData.contactsPhoneNum" placeholder=""></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="客户备注" prop="customerRemark">
                            <el-input v-model="formData.customerRemark" placeholder="" type="textarea"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </xyy-panel>
        <xyy-panel>
            <el-form ref="form" v-model="form" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="6" :md="6">
                        <el-form-item label="销售单号">
                            <el-input v-model="form.salesOrderCode" placeholder="" readonly>
                                <i slot="suffix" class="el-icon-search" @click="searchSalesOrderCode"></i>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="6" :md="6">
                        <el-form-item label="客户名称">
                            <el-input v-model="form.customerName" placeholder="" readonly></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <!-- 按钮组 start-->
            <btn-group slot="tools" :btn-list="btnList" />
            <!-- 按钮组 end-->
            <!-- 表格 列表-->
            <div v-table class="table-box">
                <vxe-grid ref="xTable" height="auto" :key="tableKey" :loading="loading" :data="tableData"
                    :columns="columns" row-id="goodsAllocationId" :row-config="{ isCurrent: true, isHover: true }"
                    :seq-config="{
                        startIndex: (tablePage.pageNum - 1) * tablePage.pageSize,
                    }" :row-class-name="getRowClassName" @cell-click="handleRowClick">
                    <template #card="{ row }">
                        <span>{{ row.card == '1' ? '是' : '否' }}</span>
                    </template>
                    <template #verdict="{ row }">
                        <el-select v-model="row.verdict" @change="verdictChange(row)">
                            <el-option :value="''" :label="'请选择'"></el-option>
                            <el-option :value="'0'" :label="'合格'"></el-option>
                            <el-option :value="'1'" :label="'不合格'"></el-option>
                        </el-select>
                    </template>
                    <template #cardStartIndex="{ row }">
                        <el-input :disabled="row.card != '1'" :value="row.cardStartIndex" placeholder=""
                            @input="(val) => { cardStartIndexHandleInput(val, row) }"></el-input>
                    </template>
                    <template #cardEndIndex="{ row }">
                        <el-input :disabled="row.card != '1'" :value="row.cardEndIndex" placeholder=""
                            @input="(val) => { cardEndIndexHandleInput(val, row) }"></el-input>
                    </template>
                    <template #salesReturnNum="{ row }">
                        <el-input :value="row.salesReturnNum" placeholder=""
                            @input="(val) => { salesReturnNumHandleInput(val, row) }"></el-input>
                    </template>
                    <template #pushstockposition="{ row }">
                        <el-select v-model="row.goodsAllocation" placeholder="请选择" filterable
                            @click.native.stop="onSelectClick(row)" @change="goodsAllocationChange(row)"
                            :disabled="row.verdict != '0'">
                            <el-option v-for="item in row.options" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </template>
                    <template #operation="{ row, column, rowIndex, columnIndex }">
                        <el-button :disabled="row.disabled" type="primary" size="small"
                            @click="delRow(row, rowIndex)">删除</el-button>
                        <el-button v-if="Number(row.salesOutNum) > 1" type="primary" size="small"
                            @click="deportation(row, rowIndex)">拆行</el-button>
                    </template>
                </vxe-grid>
            </div>
            <!-- <div class="pager">
          <vxe-pager
            border
            :current-page="tablePage.pageNum"
            :page-size="tablePage.pageSize"
            :total="tablePage.total"
            :layouts="[
              'PrevPage',
              'JumpNumber',
              'NextPage',
              'FullJump',
              'Sizes',
              'Total',
            ]"
            @page-change="handlePageChange"
          />
        </div> -->
        </xyy-panel>
        <add-dialog ref="addDialog" @search="addData"></add-dialog>
        <search-dialog ref="searchDialog" @search="searchData"></search-dialog>
        <xyy-dialog ref="dialogTableVisible" title="拆行数量" width="200" height="100">
            <el-form ref="form" label-width="120px" class="clearfix">
                <el-row :gutter="20">
                    <el-col :lg="12" :md="12">
                        <el-form-item label="拆行数量">
                            <el-input v-model="current" @input="currentHandleInput" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div slot="footer">
                <el-button type="primary" @click="confirm">确定</el-button>
                <el-button @click="() => { this.$refs.dialogTableVisible.close() }">取消</el-button>
            </div>
        </xyy-dialog>
    </div>
</template>
<script>
import {
    apiReturnUpdate,
    apiReturnSave,
    apiProductInfoList,
    apiStorageOrderPageList,
    apiDetailSeach,
    recommendGoodsAllocation
} from "@/api/heye/manage.js";
import {
    apiGetPosition,
} from "@/api/heye/pushStock";
import XEUtils from "xe-utils";
import { getNextAreaListByCode } from "@/api/outstock/fourlv.js";
import AddDialog from './components/addDialog.vue';
import SearchDialog from './components/searchDialog.vue';
import { assign } from 'xe-utils';
const start = XEUtils.getWhatMonth(new Date(), 0, "first");
const defaultBeginTime = XEUtils.toDateString(start, "yyyy-MM-dd");
const defaultEndTime = XEUtils.toDateString(new Date(), "yyyy-MM-dd");
export default {
    name: "qykxtglxthxxlr",
    components: { AddDialog, SearchDialog },
    data() {
        var validatePass = (rule, value, callback) => {
            if (!value) {
                callback();
            } else {
                if (!/^1[3456789]\d{9}$/.test(value)) {
                    callback(new Error('手机号格式不正确'));
                } else {
                    callback();
                }
            };
        }
        return {
            formDataRules: {//上半部分信息必填
                provinceName: [
                    { required: true, message: '请选择寄件地址-省份', trigger: 'change' },
                ],
                cityName: [
                    { required: true, message: '请选择寄件地址-市', trigger: 'change' },
                ],
                regionalName: [
                    { required: true, message: '请选择寄件地址-县/区', trigger: 'change' },
                ],
                detailedAddress: [
                    { required: true, message: '请选择寄件地址-详细地址', trigger: 'blur' },
                ],
                deliveryCompanies: [
                    { required: true, message: '请选择快递公司', trigger: 'change' },
                ],
                logisticsNo: [
                    { required: true, message: '请输入快递单号', trigger: 'blur' },
                ],
                packageNum: [
                    { required: true, message: '请输入包裹数', trigger: 'blur' },
                ],
                customerRemark: [
                    { required: true, message: '请输入客户备注', trigger: 'blur' },
                ],
                contacts: [
                    { required: true, message: '请输入联系人', trigger: 'blur' },
                ],
                contactsPhoneNum: [
                    { required: true, validator: validatePass, trigger: 'blur' },
                ],
            },
            provinData: [],
            cityData: [],
            districtData: [],
            deliveryCompaniesOptions: [ // 快递公司
                {
                    value: null,
                    label: "请选择",
                },
                {
                    value: "JING_DONG",
                    label: "京东",
                },
                {
                    value: "SHUN_FENG",
                    label: "顺丰",
                },
                {
                    value: "ZHONG_TONG",
                    label: "中通",
                },
                {
                    value: "YOU_ZHENG",
                    label: "邮政",
                },
                {
                    value: "SHEN_TONG",
                    label: "申通",
                },
                {
                    value: "OTHER",
                    label: "其他",
                },
            ],
            btnList: [
                {
                    label: "返回",
                    type: "danger",
                    clickEvent: this.reback,
                    code: 'btn:wms:qykxtglxthxxlr:fh',
                },
                {
                    label: "新增商品",
                    type: "danger",
                    clickEvent: this.openDialog,
                    code: 'btn:wms:qykxtglxthxxlr:xzsp'
                },
                {
                    label: "保存",
                    type: "primary",
                    clickEvent: this.submit,
                    disabled: false,
                    code: 'btn:wms:qykxtglxthxxlr:bc',
                },
            ],
            form: {
                salesOrderCode: '',//销售单号
                customerName: '',//客户名称
            },
            formData: {
                detailedAddress: '',//详细地址
                provinceName: "",
                provinceCode: '',
                cityCode: '',
                cityName: "",
                regionalName: '',
                regionalCode: "",
                customerRemark: '',//客户备注
                deliveryCompanies: null,//快递公司
                logisticsNo: '',//快递单号
                packageNum: '',//包裹数
                contacts: '',//联系人
                contactsPhoneNum: ''//联系人电话
            },
            loading: false,
            columns: this.columnsa(),
            tableData: [
            ], // 表格数据
            tablePage: {
                pageNum: 1,
                pageSize: 100,
                total: 0,
            },
            currentRow: null,  //当前点击行
            isVisible: false,
            receivedData: null,
            btnDisable: false,
            salesReturnNo: null,
            detailList: new Map(),
            current: null,
            currentIndex: null,
            tableKey: 1,
        };
    },
    // created() {

    // },
    // mounted() {
    //     if (this.$route.query.salesReturnNo) {
    //         this.salesReturnNo = this.$route.query.salesReturnNo;
    //         console.log('this.$store.state.tagsView.cachedViews', this.$store.state.tagsView.cachedViews);
    //         this.detail();
    //     }
    //     this.apigetNextAreaListByCode(0, "provin");
    // },
    activated() {
        if (this.$route.query.salesReturnNo) {
            this.salesReturnNo = this.$route.query.salesReturnNo;
            console.log('this.$store.state.tagsView.cachedViews', this.$store.state.tagsView.cachedViews);
            this.detail();
        }
        this.apigetNextAreaListByCode(0, "provin");
        this.$nextTick(() => {
            this.$refs.formData.resetFields();
        })
    },
    methods: {
        /**设置高亮 */
        getRowClassName({ row }) {
            return row.flag ? 'row--current' : '';
        },
        /**拆行弹窗输入 */
        currentHandleInput(event) {
            // 只允许输入整数
            let newValue = event.replace(/[^\d]/g, '');
            // 确保大于零
            if (newValue !== '' && parseInt(newValue, 10) <= 0) {
                newValue = '';
            }
            console.log(this.tableData[this.currentIndex].salesOutNum, 0);
            if (this.tableData[this.currentIndex].salesOutNum < Number(newValue)) {
                this.current = this.tableData[this.currentIndex].salesOutNum - 1;
                this.$message.error(`拆行数需要小于${this.tableData[this.currentIndex].salesOutNum}`);
            } else {
                this.current = newValue;
            }
            console.log(this.current, 0);
        },
        /**拆行确认 */
        confirm() {
            console.log(this.current, 1);
            let num = Number(this.current);
            let newNum = Number(this.tableData[this.currentIndex].salesOutNum) - Number(this.current);
            let newTableData = JSON.parse(JSON.stringify(this.tableData));
            newTableData[this.currentIndex].salesOutNum = newNum;
            let value = { ...this.tableData[this.currentIndex] };
            value.salesReturnNum = '';
            value.cardStartIndex = '';
            value.cardEndIndex = '';
            value.salesOutNum = num;
            value.flag = false;
            if (!value.chId) {
                value.chId = value.productCode + new Date().getTime();
                newTableData[this.currentIndex].chId = value.chId;
            }
            value.goodsAllocationId = this.tableData.length + 1;
            newTableData.splice(this.currentIndex + 1, 0, value);
            this.tableData = newTableData;
            console.log('newTableData', this.tableData);
            this.$refs.dialogTableVisible.close();
            // this.$refs.xTable.clearCurrentRow(); // 清除所有选 
            console.log(this.tableData, 'mtnn');
        },
        /**拆行 */
        deportation(row, rowIndex) {
            this.current = row.salesOutNum - 1;
            this.currentIndex = rowIndex;
            this.$refs.dialogTableVisible.open()
        },
        /**返回 */
        reback() {
            // const _this = this;
            // // this.$router.back()
            // _this.$utils.closeTabs({ reload: true }); // 默认返回列表页面
            this.$store.state.tagsView.visitedViews =
                this.$store.state.tagsView.visitedViews.filter(
                    (item) => item.name !== "qykxtglxthxxlr"
                );
            this.$router.replace({
                path: '/hyqyk/qykxtgl/qykxtglxtrk',
            });
        },
        packageNumInput(val) {//包裹数校验
            console.log(val)
            if (/^([1-9][0-9]*)?$/.test(val)) {
                this.formData.packageNum = val
            }
        },
        goodsAllocationChange(row) {
            const newTableData = [...this.tableData];
            Vue.set(this, 'tableData', newTableData);
        },
        verdictChange(val) { //入库货位
            if (val.verdict == '1') {
                val.goodsAllocation = 'BHG-01';
                // let newTableData = JSON.parse(JSON.stringify(this.tableData));
                // newTableData = newTableData.flter(item => {
                //   return item.productCode == val.productCode
                // })
                // let flag = true;
                // this.tableData.forEach(item => {
                //   if(item.productCode == val.productCode){
                //     if(item.verdict == '0'){
                //       flag = flag;
                //     }
                //   }
                // })
                // if(flag){
                //   this.detailList.delete(val.productCode);
                // }
                // console.log(this.detailList,1111);
            } else {
                val.goodsAllocation = null;
                // this.onSelectClick(val)
                // let params = {};
                // params.productCode = val.productCode;
                // params.goodsAllocationId = val.goodsAllocationId;
                // params.detailList = [];
                // this.tableData.forEach(item=>{
                //   params.detailList.push({verdict:item.verdict,productCode:item.productCode,goodsAllocation:item.goodsAllocation,goodsAllocationId:item.goodsAllocationId})
                // })
                // recommendGoodsAllocation(params).then(res=>{
                //   const { code, msg, result } = res;
                //   if(code === 0){
                //     console.log(result);
                //     val.goodsAllocation = result;
                //   } else {
                //   this.$message.error(msg);
                // }
                // })
            }
        },
        //删除行
        delRow(row, rowIndex) {
            this.tableData.splice(rowIndex, 1);
            if (!row.flag && row.chId) {
                this.tableData.map(item => {
                    if (item.chId == row.chId && item.flag) {
                        item.salesOutNum += Number(row.salesOutNum);
                    }
                })
                this.$refs.xTable.clearCheckboxRow();
                // this.current = null;
            } else if (row.flag && row.chId) {
                this.tableData = this.tableData.filter(item => {
                    return item.chId != row.chId
                })
            }
            let id = 1;
            this.tableData.forEach(item => {
                item.goodsAllocationId = id++
            })
            console.log(this.tableData, '删除行');
        },
        detail() { //编辑数据回显
            let salesNo = this.$route.query.salesReturnNo;
            apiDetailSeach(`?salesReturnNo=${salesNo}`).then((res) => {
                const { code, msg, result } = res;
                if (code === 0) {
                    this.tableData = result.detailList;
                    let id = 1;
                    this.tableData.forEach(item => {
                        item.goodsAllocationId = id++;
                        if (item.salesOutNum) {
                            item.flag = true;
                            item.chId = '';
                        }
                    })
                    console.log(this.tableData, '初始化');
                    result.detailList.forEach(item => {
                        if (item.verdict == '0') {
                            this.detailList.set(item.productCode, item.goodsAllocation)
                        }
                    })
                    const data = assign({}, result);
                    this.form.customerName = data.customerName;
                    this.form.salesOrderCode = data.salesOrderCode;
                    delete data.detailList;
                    delete data.receivePhotos;
                    this.formData = data;
                    console.log('data', this.formData, this.detailList);
                } else {
                    this.$message.error(msg);
                }
            });

        },
        //点击获取货位
        onSelectClick(row) {
            const params = {
                productCode: row.productCode
            }
            this.loading = true
            apiGetPosition(params).then((res) => {
                const { code, msg, result } = res;
                if (code === 0) {
                    //0903解决线上数据修改
                    this.tableData.forEach(item => {
                        if (item.goodsAllocationId === row.goodsAllocationId) {
                            item.options = result || []
                        }
                    })
                    // let option = result || [];
                    // if(option.length > 0){
                    //   if(this.detailList.get(row.productCode)){
                    //       row.goodsAllocation = this.detailList.get(row.productCode)
                    //     }else{
                    //       for (const [key, value] of this.detailList) {
                    //         const index = option.indexOf(value);
                    //         if (index > -1) {
                    //           option.splice(index, 1);
                    //         }
                    //       }
                    //       if(option.length > 0){
                    //         row.goodsAllocation = option[0];
                    //         this.detailList.set(row.productCode,row.goodsAllocation)
                    //         console.log(this.detailList,'0000');
                    //       }else{
                    //         this.$message.error('该商品无货位');
                    //       }
                    //     }
                    // }else{
                    //   this.$message.error('该商品无货位');
                    // }
                } else {
                    this.tableData.options = [];//0711
                    this.$message.error(msg);
                }
                this.loading = false;
                console.log("tableData", this.tableData)
            });
        },
        addData(row) {//新增
            // 复制tableData数组
            const newTableData = [...this.tableData];
            // 复制row对象
            const newRow = { ...row };
            // 新增属性
            newRow.salesReturnNum = '';
            newRow.cardStartIndex = '';
            newRow.cardEndIndex = '';
            newRow.verdict = '';
            newRow.card = newRow.isCard;
            newRow.goodsAllocation = null;
            newRow.goodsAllocationId = newTableData.length + 1;
            console.log('mt', newRow);
            // 将复制后的对象添加到复制后的tableData数组中
            newTableData.push(newRow);
            // 更新tableData数组
            this.tableData = newTableData;
        },
        searchData(row) {//销售单号查询弹窗回调
            this.form.customerName = row.clientName;
            this.form.salesOrderCode = row.orderCode;
            apiProductInfoList(`?orderCode=${row.orderCode}`).then(res => {
                const { code, msg, result } = res;
                if (code === 0) {
                    if (result.length != 0) {
                        result.forEach(item => {
                            item.salesReturnNum = '';
                            item.cardStartIndex = '';
                            item.cardEndIndex = '';
                            item.verdict = '';
                            item.salesOutNum = item.quantity;
                            item.card = item.isCard;
                            item.goodsAllocation = null;
                        })
                        this.tableData = result;
                        let id = 1;
                        this.tableData.forEach(item => {
                            item.goodsAllocationId = id++;
                            item.flag = true;
                            item.chId = '';
                        })
                        console.log(this.tableData, '删除行');
                    }
                    // this.tablePage.pageNum = pageNum || 1;
                    // this.tablePage.total = total;
                } else {
                    this.tableData = [];
                    // this.tablePage.pageNum = 1;
                    // this.tablePage.total = 0;
                    this.$message.error(msg);
                }
                this.loading = false;
            })
            console.log('mt', row);
        },
        searchSalesOrderCode() { //销售单号放大镜
            this.$refs.searchDialog.open();
        },
        //改变省份
        changeAreaOfCity(name) {
            const items = this.provinData.find((item) => {
                return item.name === name;
            });
            this.formData.provinceCode = items.code;
            this.apigetNextAreaListByCode(items.code, "city");
        },
        //改变市区
        changeAreaOfDistrict(name) {
            const items = this.cityData.find((item) => {
                return item.name === name;
            });
            this.formData.cityCode = items.code;
            this.apigetNextAreaListByCode(items.code, "district");
        },
        //选择县
        changeAreaOfRegionalName(name) {
            const items = this.districtData.find((item) => {
                return item.name === name;
            });
            this.formData.regionalCode = items.code;
        },
        //获取省市区的options
        apigetNextAreaListByCode(code, type) {
            getNextAreaListByCode({ pcode: code }).then((res) => {
                const { code, msg, result } = res;
                if (code === 0) {
                    if (type === "provin") {
                        this.provinData = result;
                        this.cityData = [];
                        this.districtData = [];
                        this.streetData = [];
                    } else if (type === "city") {
                        this.cityData = result;
                        this.districtData = [];
                        this.streetData = [];
                    } else {
                        this.districtData = result;
                        this.streetData = [];
                    }
                } else {
                    this.$message.error(msg);
                }
            });
        },
        cardStartIndexHandleInput(event, row) { //限制开始卡序输入
            console.log(row);
            const regex = /^\d+$/; // 正则表达式，匹配包含零的正整数
            if (/^([0-9]*)?$/.test(event)) {
                row.cardStartIndex = event
            }
        },
        cardEndIndexHandleInput(event, row) { //限制结束卡序输入
            console.log(row);
            const regex = /^\d+$/; // 正则表达式，匹配包含零的正整数
            if (/^([0-9]*)?$/.test(event)) {
                row.cardEndIndex = event
            }
        },
        salesReturnNumHandleInput(event, row) { //限制结束卡序输入
            console.log(row);
            const regex = /^\d+$/; // 正则表达式，匹配包含零的正整数
            if (/^([0]|[1-9][0-9]*)?$/.test(event)) {
                row.salesReturnNum = event
            }
        },
        async submit() {
            let flag = await this.submitValidate();
            console.log(this.formData, this.tableData, flag);
            let params = { ...this.formData, ...this.form };
            params.detailList = this.tableData;
            if (flag == true) {
                if (!this.$route.query.salesReturnNo) {
                    this.btnList[1].disabled = true;
                    apiReturnSave(params).then(res => {
                        const { code, msg, result } = res;
                        if (code === 0) {
                            this.$message({
                                message: msg,
                                type: 'success'
                            });
                            this.reback();
                            // this.$router.push({
                            //   path:'/heyeenablecard/cancelDismiss/returnStock'
                            // })
                        } else {
                            this.$message.error(msg);
                        }
                        this.btnList[1].disabled = false;
                    })
                } else {
                    this.btnList[1].disabled = true;
                    params.salesReturnNo = this.$route.query.salesReturnNo;
                    apiReturnUpdate(params).then(res => {
                        const { code, msg, result } = res;
                        if (code === 0) {
                            this.$message({
                                message: msg,
                                type: 'success'
                            });
                            this.reback();
                            // this.$router.push({
                            //   path:'/heyeenablecard/cancelDismiss/returnStock'
                            // })
                        } else {
                            this.$message.error(msg);
                        }
                        this.btnList[1].disabled = false;
                    })
                }
            } else {
                this.$message({
                    message: flag,
                    type: 'warning'
                });
            }
        },
        submitValidate() {//保存校验
            return new Promise((resolve, reject) => {
                this.$refs['formData'].validate((valid) => {
                    if (valid) {
                        let data = this.tableData;
                        let msg = '';
                        for (let index = 0; index < data.length; index++) {
                            msg = `表格第${index + 1}行`
                            if (data[index].salesReturnNum) {
                                if (data[index].salesOutNum && (Number(data[index].salesReturnNum) > Number(data[index].salesOutNum))) {
                                    msg += `销售退回数量不能大于销售出库数量`
                                    // this.$message({
                                    //   message: msg,
                                    //   type: 'warning'
                                    // });
                                    resolve(msg);
                                } else {
                                    if (!data[index].verdict) {
                                        msg += `收货结论必选`
                                        // this.$message({
                                        //   message: msg,
                                        //   type: 'warning'
                                        // });
                                        resolve(msg);
                                    } else {
                                        if (!data[index].goodsAllocation) {
                                            msg += `入库货位必填`
                                            resolve(msg);
                                        }
                                    }
                                }
                            } else {
                                msg += `销售退回数量不能为空`
                                resolve(msg);
                            }
                            if (data[index].card == '1') {
                                if (data[index].cardStartIndex) {
                                    if (data[index].cardEndIndex) {
                                        if (data[index].cardEndIndex.length != data[index].cardStartIndex.length) {
                                            msg += `结束卡序和开始卡序长度必须一样`;
                                            // this.$message({
                                            //   message: msg,
                                            //   type: 'warning'
                                            // });
                                            resolve(msg);
                                        } else if (Number(data[index].cardEndIndex) < Number(data[index].cardStartIndex)) {
                                            msg += `结束卡序必须大于等于开始卡序`;
                                            // this.$message({
                                            //   message: msg,
                                            //   type: 'warning'
                                            // });
                                            resolve(msg);
                                        }
                                    } else {
                                        msg += `结束卡序不能为空`
                                        // this.$message({
                                        //   message: msg,
                                        //   type: 'warning'
                                        // });
                                        resolve(msg);
                                    }
                                } else {
                                    msg += `开始卡序不能为空`
                                    // this.$message({
                                    //   message: msg,
                                    //   type: 'warning'
                                    // });
                                    resolve(msg);
                                }
                            }
                        }
                        resolve(true)
                    } else {
                        msg += '必填项不能为空'
                        // this.$message({
                        //   message: '请填写必填项',
                        //   type: 'warning'
                        // });
                        resolve(msg);
                    }
                })
            })
        },
        openDialog() {//新增商品
            this.$refs.addDialog.open();
        },
        columnsa() {
            return [
                { type: "seq", width: 60, title: "序号" },
                { field: "verdict", title: "收货结论", width: 200, slots: { default: 'verdict' } },
                { field: "productName", title: "商品名称", width: 200, },
                { field: "goodsAllocation", title: "入库货位", width: 150, slots: { default: 'pushstockposition' } },
                { field: "salesReturnNum", title: "销售退回数量", width: 200, slots: { default: 'salesReturnNum' } },
                { field: "salesOutNum", title: "销售出库数量", width: 200, },
                { field: "productBarCode", title: "商品条码", width: 200, },
                { field: "card", title: "是否权益卡商品", width: 200, slots: { default: 'card' } },
                { field: "cardNoPrefix", title: "权益卡前缀", width: 200 },
                { field: "cardStartIndex", title: "开始卡序", width: 200, slots: { default: 'cardStartIndex' } },
                { field: "cardEndIndex", title: "结束卡序", width: 200, slots: { default: 'cardEndIndex' } },
                { field: "", title: "操作", slots: { default: 'operation' }, width: 200, }
            ];
        },
        handleRowClick({ row }) {//单击选中行
            this.currentRow = row
        },
    },
};
</script>
<style>
.numinput input {
    text-align: center;
}
</style>
<style scoped>
.el-row {
    margin-bottom: 10px;
}

.isdisabled {
    background-color: #f3f3f3;
}
</style>