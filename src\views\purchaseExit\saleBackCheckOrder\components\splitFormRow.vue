<template>
  <div class="split-dialog">
    <xyy-dialog title="拆分表单行" ref="splitDialog" width="948px">
      <div v-table class="table-box">
        <vxe-table
          ref="xTable"
          :loading="loading"
          highlight-current-row
          highlight-hover-row
          height="375"
          :data="tableData"
          @current-change="currentChangeEvent"
          row-id="productId"
        >
          <vxe-table-column type="seq" title="序号" width="80" />
          <template v-for="item in splitTableColumns">
            <vxe-table-column
              v-if="item.visible"
              :key="item.field"
              :field="item.field"
              :title="item.title"
              min-width="156px"
              max-width="220px"
            >
              <!-- 使用插槽 -->
              <template #default="{ row, rowIndex }">
                <!-- 验收评定 -->
                <span v-if="item.field === 'checkAssess'">
                  <el-select
                    v-model="row.checkAssess"
                    @change="changeReCheck(row)"
                  >
                    <el-option
                      v-for="item in optResult"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </span>
                <!-- 采取措施 -->
                <span v-else-if="item.field === 'measures'">
                  <el-select
                    v-model="row.measures"
                    :disabled="row.checkAssess == 3"
                    v-if="row.checkAssess == 1"
                  >
                    <el-option
                      v-for="item in measuresSelect1"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <el-select
                    v-model="row.measures"
                    :disabled="row.checkAssess != 2"
                    v-else-if="row.checkAssess == 2"
                  >
                    <el-option
                      v-for="item in measuresSelect2"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                  <el-select
                    v-model="row.measures"
                    :disabled="row.checkAssess != 2"
                    v-else-if="row.checkAssess == 3"
                  >
                    <el-option
                      v-for="item in measuresSelect3"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </span>
                <!-- 复查件数 -->
                <span v-else-if="item.field === 'realPiecesNum'">
                  <el-input
                    v-model.number="row.realPiecesNum"
                    :disabled="row.realScatteredNum > 0"
                    @input="checkInputPiece(row)"
                  ></el-input>
                </span>
                <!-- 复查零散数 -->
                <span v-else-if="item.field === 'realScatteredNum'">
                  <el-input
                    v-model.number="row.realScatteredNum"
                    :disabled="row.realPiecesNum > 0"
                    @input="checkInputScattered(row)"
                  ></el-input>
                </span>
                <!-- 容器编号 -->
                <span v-else-if="item.field === 'containerCode'">
                  <el-input v-model="row.containerCode" disabled>
                    <el-button
                      slot="append"
                      icon="el-icon-search"
                      @click="openCodeContainer(row, rowIndex)"
                    />
                  </el-input>
                </span>
                <!-- 如果不是，则直接显示字段内容 -->
                <span v-else>{{ row[item.field] }}</span>
              </template>
            </vxe-table-column>
          </template>
        </vxe-table>
        <p style="font-size: 20px; font-weight: bolder; float: left">
          总数量:{{ totalTwo }}
        </p>
        <!-- 按钮组 -->
        <btn-group
          slot="tools"
          :btn-list="btnList"
          style="margin-top: 25px; transform: translateY(-20px)"
        />
      </div>
    </xyy-dialog>
    <dialogContainer
      ref="dialogContainer"
      @on-close="closeCodeContainer"
    ></dialogContainer>
  </div>
</template>
<script>
import { checkInputNorm } from "../../../../utils/jsjUtils";
import dialogContainer from "./dialogContainer.vue";
import { splitTableColumns } from "../config";
export default {
  name: "splitFormRowDialog",
  components: {
    dialogContainer,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      oldSelectData: "",
      splitTableColumns: splitTableColumns(),
      btnList: [
        {
          label: "新增",
          type: "primary",
          icon: "el-icon-circle-plus-outline",
          code: "btn:wms:saleBackCheckOrderDetail:newAdd",
          clickEvent: this.newAdd,
        },
        {
          label: "删除",
          type: "danger",
          icon: "el-icon-delete",
          code: "btn:wms:saleBackCheckOrderDetail:delete",
          clickEvent: this.deleteInfo,
        },
        {
          label: "确认",
          type: "primary",
          icon: "el-icon-check",
          code: "btn:wms:saleBackCheckOrderDetail:notarize",
          clickEvent: this.notarize,
        },
        {
          label: "关闭",
          type: "warning",
          icon: "el-icon-close",
          code: "btn:wms:saleBackCheckOrderDetail:close",
          clickEvent: this.closeDialog,
        },
      ],
      // 验收评定
      optResult: [
        {
          value: 1,
          label: "合格",
        },
        {
          value: 2,
          label: "不合格",
        },
        {
          value: 3,
          label: "待处理",
        },
      ],
      // 采取措施
      measuresSelect1: [
        {
          value: "1",
          label: "入合格库",
        },
        // {
        //   value: "5",
        //   label: "入特价库",
        // },
      ],
      measuresSelect2: [],
      measuresSelect3: [],
      selectData: "",
      oldTableData: [],
      // 复查数量和
      total: 0,
      totalTwo: 0,
    };
  },
  methods: {
    // 校验件数
    checkInputPiece(row) {
      row.containerCode = "";
      console.log(row);
      row.realPiecesNum = checkInputNorm(row.realPiecesNum);
      this.computReviewNum(row);
      row.realReturnNum = row.returnNum;
    },
    // 校验零散数
    checkInputScattered(row) {
      row.containerCode = "";
      row.realScatteredNum = checkInputNorm(row.realScatteredNum);
      this.computReviewNum(row);
      row.realReturnNum = row.returnNum;
    },
    // 计算复查退回数量
    computReviewNum(row) {
      row.returnNum =
        Number(row.realPiecesNum) * Number(row.packageNum) +
        Number(row.realScatteredNum);
      if (row.returnNum == 0) {
        row.containerCode = "";
      }
    },
    // 打开弹框执行的回调
    open(fatherSelectData) {
      this.$refs.splitDialog.open();
      console.log(fatherSelectData);
      this.totalTwo = 0;
      fatherSelectData.forEach((item) => {
        this.totalTwo = this.totalTwo + item.realReturnNum;
      });

      this.oldSelectData = JSON.parse(JSON.stringify(fatherSelectData[0]));
      this.tableData = JSON.parse(JSON.stringify(fatherSelectData));
      console.log(this.tableData);

      // 深拷贝一份初始数据
      this.oldTableData = JSON.parse(JSON.stringify(this.tableData));
      // 对采取措施列进行初始化
      this.tableData.forEach((item) => {
        this.changeReCheck(item);
      });
    },
    // 关闭弹框执行的回调
    closeDialog() {
      this.tableData = [];
      this.$refs.splitDialog.close();
    },
    // id随机值
    genID() {
      const length = 10;
      return Number(
        Math.random().toString().substr(3, length) + Date.now()
      ).toString(36);
    },
    // 新增行执行的回调
    newAdd() {
      let newData = this.oldSelectData;
      newData.productId = this.genID();
      newData = JSON.parse(JSON.stringify(newData));
      this.tableData.push(newData);
    },
    // 删除行执行的回调
    deleteInfo() {
      if (this.selectData == "") {
        this.$message.warning("请选择要删除的行!");
        console.log(this.selectData.productId);
        return;
      }
      //一行数据不可以删除
      if (this.tableData.length === 1) {
        this.$message.warning("最后一行无法删除!");
        return;
      }
      //筛选删除项
      this.tableData = this.tableData.filter((item) => {
        return item.productId != this.selectData.productId;
      });
      this.selectData = "";
    },
    // 确认执行的回调
    notarize() {
      this.total = 0;
      this.tableData.forEach((item) => {
        this.total =
          +(item.realPiecesNum && !item.realScatteredNum
            ? item.realPiecesNum * item.packageNum
              ? item.realPiecesNum * item.packageNum
              : "0"
            : item.realScatteredNum
            ? item.realScatteredNum
            : "0") + this.total;
      });
      console.log(this.total);

      if (this.total != this.totalTwo) {
        this.$message.error("实际退回数量不等于验收数量!");
        return;
      }
      //将拆分或合并的数据传给父组件
      this.$emit("sendData", this.tableData);
      this.closeDialog();
    },
    // 切换验收评定执行的回调
    changeReCheck(row) {
      // 合格
      if (row.checkAssess == 1) {
        row.measures = "1";
        this.measuresSelect1 = [
          {
            value: "1",
            label: "入合格库",
          },
          // {
          //   value: "5",
          //   label: "入特价库",
          // },
        ];
        // 容器编号为初始值
        this.oldTableData.forEach((oldRow) => {
          if (row.id === oldRow.id) {
            row.containerCode = oldRow.containerCode;
          }
        });
      } else if (row.checkAssess == 2) {
        row.measures = "2";
        this.measuresSelect2 = [
          {
            value: "3",
            label: "入不合格库",
          },
          {
            value: "2",
            label: "入退货区",
          },
        ];
        // 清空容器编号
        row.containerCode = "";
      } else if (row.checkAssess == 3) {
        row.measures = "4";
        this.measuresSelect3 = [
          {
            value: "4",
            label: "移入待处理区，等待复查",
          },
        ];
        // 清空容器编号
        row.containerCode = "";
      }
    },
    // 打开容器编号弹框
    openCodeContainer(row, rowIndex) {
      if (this.selectData.returnNum == 0) {
        this.numAllIsZero();
      } else {
        this.$refs.dialogContainer.open(row, rowIndex);
      }
    },
    // 关闭容器编号弹框
    closeCodeContainer({ row, rowIndex }) {
      this.tableData[rowIndex].containerCode =
        row.data[row.$rowIndex].containerCode;
    },
    //  // 容器弹窗选择关闭
    //  onContainerClose({ row, rowIndex }) {
    //   // console.log(row,rowIndex);
    //   // this.tableData[rowIndex].containerCode = row.containerCode;
    //   // console.log(this.productData);
    //   this.tableData[rowIndex].containerCode =
    //     row.data[row.$rowIndex].containerCode;
    // },
    // 单机行的回调
    currentChangeEvent(val) {
      console.log(val);

      this.selectData = val.row;
    },
    // 整件数和零散数都为0的提示
    numAllIsZero() {
      this.$alert(
        `请填写实际件数和零散数后，才可以选择相应容器！注意：该商品是整散分开的，整件和零散不能同时填写;可以通过双击进行拆行操作，分开填写！`,
        "提示",
        {
          confirmButtonText: "ok",
          callback: (action) => {},
        }
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.table-box {
  height: 400px !important;
}
</style>
