
// 判断obj是否为一个整数
function isInteger(obj) {
  return Math.floor(obj) === obj;
}

/*
 * 将一个浮点数转成整数，返回整数和倍数。如 3.14 >> 314，倍数是 100
 * @param floatNum {number} 小数
 * @return {object}
 *   {times:100, num: 314}
 */
function toInteger(floatNum) {
  const ret = { times: 1, num: 0 };
  if (isInteger(floatNum)) {
    ret.num = floatNum;
    return ret;
  }
  const strfi = floatNum + "";
  const dotPos = strfi.indexOf(".");
  const len = strfi.substr(dotPos + 1).length;
  const times = Math.pow(10, len);
  const intNum = parseInt(floatNum * times + 0.5, 10);
  ret.times = times;
  ret.num = intNum;
  return ret;
}

/*
 * 核心方法，实现加减乘除运算，确保不丢失精度
 * 思路：把小数放大为整数（乘），进行算术运算，再缩小为小数（除）
 *
 * @param a {number} 运算数1
 * @param b {number} 运算数2
 * @param op {string} 运算类型，有加减乘除（add/subtract/multiply/divide）
 *
 */
function operation(a, b, op) {
  const o1 = toInteger(a);
  const o2 = toInteger(b);
  const n1 = o1.num;
  const n2 = o2.num;
  const t1 = o1.times;
  const t2 = o2.times;
  const max = t1 > t2 ? t1 : t2;
  let result = null;
  switch (op) {
    case "add":
      if (t1 === t2) {
        // 两个小数位数相同
        result = n1 + n2;
      } else if (t1 > t2) {
        // o1 小数位 大于 o2
        result = n1 + n2 * (t1 / t2);
      } else {
        // o1 小数位 小于 o2
        result = n1 * (t2 / t1) + n2;
      }
      return result / max;
    case "subtract":
      if (t1 === t2) {
        result = n1 - n2;
      } else if (t1 > t2) {
        result = n1 - n2 * (t1 / t2);
      } else {
        result = n1 * (t2 / t1) - n2;
      }
      return result / max;
    case "multiply":
      result = (n1 * n2) / (t1 * t2);
      return result;
    case "divide":
      result = (n1 / n2) * (t2 / t1);
      return result;
  }
}

// 加减乘除的四个接口
function add(a, b) {
  return operation(a, b, "add");
}

function subtract(a, b) {
  return operation(a, b, "subtract");
}

function multiply(a, b) {
  return operation(a, b, "multiply");
}

function divide(a, b) {
  return operation(a, b, "divide");
}

function replaceNumber(value, oldReplaceVaule, min, max) {
  const oldValue = "" + value;
  let minusSigned = false;
  if (min < 0) {
    // 则表示需要考虑负数
    minusSigned = true;
  }
  // 以 负号分割，只取最后一个元素
  const array = oldValue.split("-");
  let replaceValue = array[array.length - 1];
  let first = "";
  if (replaceValue === oldValue && array.length === 1) {
    // 表示原来数据无负号
    first = "";
  } else {
    // 原数据有负号
    if (minusSigned) {
      first = "-"; // 记录负号
    } else {
      first = "";
    }
  }
  // 非数字的都替换掉
  replaceValue = replaceValue.replace(/[^\d]/g, "");
  // 抹除 开头多个 0
  replaceValue = replaceValue.replace(/^0*(0|[1-9][0-9]*)$/g, "$1");
  if (replaceValue.length === 0) {
    return first;
  }
  const num = Number(first + replaceValue);
  if (num < min) {
    // 如果小于最小值
    replaceValue = Math.abs(Number(oldReplaceVaule));
  } else if (num > max) {
    // 如果大于最大值
    replaceValue = Math.abs(Number(oldReplaceVaule));
  }
  if (first.length > 0 && replaceValue === "0") {
    return first;
  }
  return first + replaceValue;
}
//number：为你要转换的数字(四舍五入)
//format：要保留几位小数；譬如要保留2位，则值为2
//zerFill:是否补零。不需要补零可以不填写此参数
function toFixed(number, format, zeroFill) {
  //判断非空
  if (!isEmpty(number)) {
    //正则匹配:正整数，负整数，正浮点数，负浮点数
    if (!/^\d+(\.\d+)?$|^-\d+(\.\d+)?$/.test(number)) return number;
    let n = 1;
    for (let i = 0; i < format; i++) {
      n = n * 10;
    }

    //四舍五入
    number = Math.round(number * n) / n;
    let str = number.toString();

    //是否补零
    if (zeroFill) {
      let index;
      if (str.indexOf(".") == -1) {
        index = format;
        str += ".";
      } else {
        index = format - (str.length - 1 - str.indexOf("."));
      }

      for (let i = 0; i < index; i++) {
        str += "0";
      }
    }
    return str;
  }
  return number;
}

//number：为你要转换的数字(四舍五入)
//format：要保留几位小数；譬如要保留2位，则值为2
//zerFill:是否补零。不需要补零可以不填写此参数
function FktoFixedNew(number, format, zeroFill) {
  //判断非空
  if (!isEmpty(number)) {
    //正则匹配:正整数，负整数，正浮点数，负浮点数
    if (!/^\d+(\.\d+)?$|^-\d+(\.\d+)?$/.test(number)) return number;

    format = checkPrecision(format);
		var exponentialForm = Number(unformatNew(number) + 'e' + format);
		var rounded = Math.round(exponentialForm);
		number = Number(rounded + 'e-' + format).toFixed(format);
    let str = number.toString();

    //是否补零
    if (zeroFill) {
      let index;
      if (str.indexOf(".") == -1) {
        index = format;
        str += ".";
      } else {
        index = format - (str.length - 1 - str.indexOf("."));
      }

      for (let i = 0; i < index; i++) {
        str += "0";
      }
    }
    return str;
  }
  return number;
}

//你要转换的数字(四舍五入)
//format：要保留几位小数；譬如要保留2位，则值为2
//zerFill:是否补零。不需要补零可以不填写此参数
function FktoFixed(number, format, zeroFill) {
  //判断非空
  if (!isEmpty(number)) {
    //正则匹配:正整数，负整数，正浮点数，负浮点数
    if (!/^\d+(\.\d+)?$|^-\d+(\.\d+)?$/.test(number)) return number;
    let n = 1;
    for (let i = 0; i < format; i++) {
      n = n * 10;
    }
    let numbercopy = number.toString();
    let numArray = numbercopy.split('.');
    let b = '';
    let c = '0.';
    let d;
    for (let index = 0; index < format; index++) {
      b = b + '0'
    }
    if(numArray.length === 2 && numArray[1].length === format){
      c = parseFloat(c+b+'1');
      d = parseFloat(number) + c;
    }else{
      d = number;
    }
    
    //四舍五入
    number = Math.round(d * n) / n;
    let str = number.toString();

    //是否补零
    if (zeroFill) {
      let index;
      if (str.indexOf(".") == -1) {
        index = format;
        str += ".";
      } else {
        index = format - (str.length - 1 - str.indexOf("."));
      }

      for (let i = 0; i < index; i++) {
        str += "0";
      }
    }
    return str;
  }
  return number;
}

//非空验证
function isEmpty(ObjVal) {
  if (
    ObjVal == null ||
    typeof ObjVal == "undefined" ||
    (typeof ObjVal == "string" && ObjVal == "" && ObjVal != "undefined")
  ) {
    return true;
  } else {
    return false;
  }
}

function checkPrecision(val) {
  val = Math.round(Math.abs(val));
  return isNaN(val)? 2 : val;
}
function unformatNew(value, decimal) {
  value = value || 0;

  if (typeof value === "number") return value;

  decimal = decimal || 2;

   // Build regex to strip out everything except digits, decimal point and minus sign:
  var regex = new RegExp("[^0-9-" + decimal + "]", ["g"]),
    unformatted = parseFloat(
      ("" + value)
      .replace(/\((?=\d+)(.*)\)/, "-$1") // replace bracketed values with negatives
      .replace(regex, '')         // strip out any cruft
      .replace(decimal, '.')      // make sure decimal point is standard
    );

  // This will fail silently which may cause trouble, let's wait and see:
  return !isNaN(unformatted) ? unformatted : 0;
};

export default {
  add: add,
  subtract: subtract,
  multiply: multiply,
  divide: divide,
  replaceNumber: replaceNumber,
  toFixed,
  FktoFixed,
  FktoFixedNew,
};
