import request from '@/utils/request'

/************基础资料 start************* */
/**
 * 基础资料-客户资料-列表
 */
export function queryCustomerTableData(param) {
    return request({
        url: '/basicdata/master/customerBase/selectList',
        method: 'post',
        data:param
    })
}
/**
 * 基础资料-客户资料-详情
 */
export function queryCustomerDetailData(param) {
    return request({
        url: '/basicdata/master/customerBase/detail',
        method: 'post',
        data:param
    })
}
/**
 * 基础资料-客户资料-详情-保存
 */
export function saveCustomerDetailData(param) {
    return request({
        url: '/basicdata/master/customerBase/update',
        method: 'post',
        data:param
    })
}
/**
 * 基础资料-商品资料-列表
 */
export function queryGoodsinfoTableData(param) {
    return request({
        url: '/basicdata/master/productBase/page',
        method: 'post',
        data: param
    })
}
/**
 * 基础资料-商品资料-详情
 */
export function queryGoodsinfoDetailData(param) {
    return request({
        url: '/basicdata/master/productBase/getById'+param,
        method: 'get',
        data: {}
    })
}
/**
 * 基础资料-商品资料-附件列表
 */
export function queryGoodsinfoApprovalFileData(param) {
    return request({
        url: '/basicdata/master/productBase/getApprovalFileByProductId'+param,
        method: 'get',
        data: {}
    })
}
/**
 * 商品资料-枚举接口
 */
export function queryGoodsinfoDictByType(param) {
    return request({
        url: '/basicdata/dictBases/getByDictTypeList',
        method: 'post',
        data: param
    })
}

/**
 * 基础资料-商品资料-根据长宽计算体积
 */
export function queryGoodsinfoComputeVolume(param) {
    return request({
        url: '/basicdata/master/productBase/computeVolByType',
        method: 'post',
        data: param
    })
}
/**
 * 基础资料-商品资料-计算重量
 */
export function queryGoodsinfoComputeWeight(param) {
    return request({
        url: '/basicdata/master/productBase/computeWeightByType',
        method: 'post',
        data: param
    })
}
/**
 * 基础资料-商品资料-保存
 */
export function saveProductBase(param) {
    return request({
        url: '/basicdata/master/productBase/update',
        method: 'post',
        data: param
    })
}
/**
 * 基础资料-供应商资料-列表
 */
export function querySupplierTableData(param) {
    return request({
        url: '/basicdata/master/supplierBase/selectList',
        method: 'post',
        data:param
    })
}
/**
 * 基础资料-供应商资料-详情
 */
export function querySupplierDetailData(param) {
    return request({
        url: '/basicdata/master/supplierBase/supplierDetails',
        method: 'post',
        data:param
    })
}

/**
 * 基础资料-图片预览
 */
export function exceptionShowImgs(param) {
    return request({
        url: '/basicdata/master/supplierBase/supplierDetails',
        method: 'post',
        data:param
    })
}

/**
 * 基础资料-商品资料-上传中包装维护文件
 */
export function uploadWrapFile(param) {
    return request({
        url: '/basicdata/master/productBase/importMediumPackagingNum',
        method: 'post',
        data: param
    })
}
/**
 *  基础资料-商品资料-上传批量维护监管文件
 */
export function uploadBatchFile(param) {
    return request({
        url: '/basicdata/master/productBase/importWhetherRegulatory',
        method: 'post',
        data: param
    })
}
/************基础资料 end************* */