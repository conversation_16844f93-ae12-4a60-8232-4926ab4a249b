<template>
  <div>
    <xyy-dialog ref="dialogVisible" title="提交差异" width="60%" :visible.sync="outerVisible" @on-close="closeDialog">
      <!-- 差异信息显示 -->
      <div class="difference-info">
        <span class="difference-text">差异数量：</span>
        <span class="difference-value" :class="differenceClass">{{ differenceText }}</span>
      </div>

      <!-- 批号差异表格 -->
      <vxe-table ref="differenceTable" highlight-current-row highlight-hover-row height="300px" class="difference-table"
        :data="batchData" border stripe>
        <vxe-table-column type="seq" title="序号" width="80" />
        <vxe-table-column field="productCode" title="商品编码" width="120" />
        <vxe-table-column field="batchNumber" title="商品批号" width="120" />
        <vxe-table-column field="productName" title="商品名称" width="150" />
        <vxe-table-column field="reviewNumber" title="复核数量" width="120" />

        <!-- 实际数量输入 -->
        <vxe-table-column field="actualNumber" title="实际数量" width="120">
          <template v-slot="{ row }">
            <el-input-number v-model="row.actualNumber" :min="0" :precision="0" size="small"
              @change="handleActualNumberChange" />
          </template>
        </vxe-table-column>
      </vxe-table>

      <!-- 操作按钮 -->
      <div class="dialog-footer" slot="footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">提交</el-button>
      </div>
    </xyy-dialog>
  </div>
</template>

<script>
import { submitException } from "@/api/outstock/distribution";

export default {
  name: "QuantityDifferenceDialog",
  data() {
    return {
      outerVisible: false,
      batchData: [], // 批号数据
      inputQuantity: 0, // 用户输入的数量
      originalQuantity: 0, // 原始复核数量
      mergeOrderCode: "", // 合单单号
    };
  },
  computed: {
    // 差异数量
    differenceQuantity() {
      return this.inputQuantity - this.originalQuantity;
    },

    // 差异文本
    differenceText() {
      const diff = Math.abs(this.differenceQuantity);
      const type = this.differenceQuantity > 0 ? "多" : "少";
      // 安全访问 packingUnit 属性，提供默认值
      const packingUnit = this.batchData.length > 0 && this.batchData[0]
        ? (this.batchData[0].packingUnit || "个")
        : "个";
      return `${type}${diff}${packingUnit}`;
    },

    // 差异样式类
    differenceClass() {
      return this.differenceQuantity > 0 ? "difference-more" : "difference-less";
    },

    // 实际数量总和
    actualTotal() {
      return this.batchData.reduce((sum, item) => sum + (item.actualNumber || 0), 0);
    }
  },
  methods: {
    // 打开弹窗
    open(productData, inputQuantity) {
      this.inputQuantity = inputQuantity;
      // 修复：使用汇总的复核数量作为原始数量
      this.originalQuantity = productData.totalReviewNumber || productData.reviewNumber || 0;
      this.mergeOrderCode = productData.mergeOrderCode || "";

      // 安全检查并构建批号数据
      if (productData.batches && Array.isArray(productData.batches)) {
        this.batchData = productData.batches.map(batch => ({
          productCode: productData.productCode || "",
          productName: productData.productName || "",
          batchNumber: batch.batchNumber || "",
          reviewNumber: batch.reviewNumber || 0,
          actualNumber: batch.reviewNumber || 0, // 默认值为复核数量
          specifications: batch.specifications || "",
          packingUnit: batch.packingUnit || "个", // 提供默认单位
          producingArea: batch.producingArea || "",
          manufacturer: batch.manufacturer || ""
        }));
      } else {
        // 如果没有批号数据，创建一个默认的批号项
        this.batchData = [{
          productCode: productData.productCode || "",
          productName: productData.productName || "",
          batchNumber: "默认批号",
          reviewNumber: this.originalQuantity,
          actualNumber: this.originalQuantity,
          specifications: productData.specifications || "",
          packingUnit: productData.packingUnit || "个",
          producingArea: "",
          manufacturer: ""
        }];
      }

      this.outerVisible = true;
    },

    // 实际数量变化处理
    handleActualNumberChange() {
      // 可以在这里添加实时验证逻辑
    },

    // 确认提交
    confirm() {
      // 验证实际数量总和是否等于输入数量
      if (this.actualTotal !== this.inputQuantity) {
        this.$message.error("实际数量需与复核输入数量一致");
        this.$emit("on-validation-failed");
        this.closeDialog();
        return;
      }

      // 构建异常提交数据
      const exceptionData = [];
      this.batchData.forEach(batch => {
        const difference = batch.actualNumber - batch.reviewNumber;
        if (difference !== 0) {
          exceptionData.push({
            mergeOrderCode: this.mergeOrderCode,
            productCode: batch.productCode,
            batchNumber: batch.batchNumber,
            exceptionNumber: Math.abs(difference),
            exceptionCause: difference > 0 ? "2" : "1", // 2:多货, 1:少货
            exceptionReason: "1", // 默认原因
            specifications: batch.specifications,
            packingUnit: batch.packingUnit,
            producingArea: batch.producingArea,
            manufacturer: batch.manufacturer
          });
        }
      });

      if (exceptionData.length > 0) {
        // 调用异常提交API
        submitException(exceptionData).then((res) => {
          const { code, msg } = res;
          if (code === 0) {
            this.$message.success("差异提交成功");
            this.$emit("on-difference-submitted", {
              batchData: this.batchData,
              actualQuantity: this.inputQuantity
            });
            this.closeDialog();
          } else {
            this.$message.error(msg);
          }
        });
      } else {
        // 没有差异，直接继续复核流程
        this.$emit("on-difference-submitted", {
          batchData: this.batchData,
          actualQuantity: this.inputQuantity
        });
        this.closeDialog();
      }
    },

    // 取消
    cancel() {
      this.$emit("on-cancelled");
      this.closeDialog();
    },

    // 关闭弹窗
    closeDialog() {
      this.outerVisible = false;
      this.batchData = [];
      this.inputQuantity = 0;
      this.originalQuantity = 0;
      this.mergeOrderCode = "";
    }
  }
};
</script>

<style scoped>
.difference-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  text-align: center;
}

.difference-text {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.difference-value {
  font-size: 18px;
  font-weight: bold;
  margin-left: 10px;
}

.difference-more {
  color: #f56c6c;
  /* 红色表示多货 */
}

.difference-less {
  color: #e6a23c;
  /* 橙色表示少货 */
}

.difference-table {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}
</style>
