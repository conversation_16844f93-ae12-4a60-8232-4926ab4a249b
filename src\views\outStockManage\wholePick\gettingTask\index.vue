<template>
  <div class="app-container">
    <xyy-panel title="整件索取">
      <!-- 按钮组 start-->
      <btn-group slot="tools" :btn-list="btnList" style="margin-right: 60px;" />
      <div class="statisticsBtn" v-if="getPermission('btn:wms:gettingTask:openStatistics')" @mouseenter="openStatistics"
        @mouseleave="clostStatistics">
        <el-button type="primary">
          <svg aria-hidden="true"
            style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
            viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headshujutongji" />
          </svg>
        </el-button>
      </div>
      <!-- 按钮组 end-->
      <!-- 查询条件 start -->
      <el-form ref="formData" v-model="formData" label-width="120px" class="clearfix">
        <el-row>
          <el-col :lg="6" :md="6">
            <el-form-item label="单据编号">
              <el-input v-model="formData.orderCode" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="商品编码">
              <el-input v-model="formData.productCode" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="6">
            <el-form-item label="拣货员工号">
              <el-input v-model="formData.employeeNumber" placeholder="请输入" @keydown.enter.native="entryJobNo(true)" />
              <span>{{ jobName }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :lg="6" :md="6">
          <el-form-item label="高优出库">
            <el-select v-model="formData.highLevel" placeholder="请选择">
              <el-option label="全部" value=""></el-option>
              <el-option v-for="item in highLevel" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="6" style="display: flex;height:44px;align-items: center;margin-left:20px;">
          <el-checkbox v-model="isPrintWholePicking" @change="checkPickChange">索取后自动打印拣货单</el-checkbox>
          <el-checkbox v-model="isPrintWholePickingTag" @change="checkPickTagChange">索取后自动打印标签</el-checkbox>
        </el-col>
      </el-form>
    </xyy-panel>
    <!-- 查询条件 -->
    <el-card>
      <div class="heade-line">
        <div class="title">整件任务列表</div>
        <!-- <div ref="rowMessage" class="row-message-class">
          <tween-message :message="tweenMessage" :stopBoole="stopBoole" :tweenwindth="tweenWidth"
            :frist-move="fristMove" />
        </div> -->
        <!-- <el-button class="setField" type="primary" v-if="getPermission('btn:wms:gettingTask:setingTableDataHander')"
          @click="setingTableDataHander" icon="el-icon-setting" /> -->
        <!--筛选列组件-->
        <!-- <div slot="tools" style="float:right; position: relative;"> -->
        <el-button class="setField" type="primary" @click="setingTableDataHander(0)">
          <svg aria-hidden="true"
            style="width: 1em; height: 1em; position: relative; top: 2px; fill: currentColor; overflow: hidden;"
            viewBox="0 0 1024 1024">
            <!-- # + iconfont项目内symbol图标代码 -->
            <use xlink:href="#icon-headxitongguanli" />
          </svg>
        </el-button>
        <!-- </div> -->
      </div>
      <div class="summary">
        <div class="list">
          <div class="side-right">待索取绑定随货同行单整件任务：</div>
          <div class="side-right">{{ bindGoodOrderRequestedList }}</div>
        </div>
        <div class="list">
          <div class="side-right">待索取未绑定随货同行单整件任务：</div>
          <div class="side-right">{{ unBindGoodOrderRequestedList }}</div>
        </div>
        <!-- <div class="list">
          <div class="side-right">待领取的补货任务数：</div>
          <div class="side-right">{{ waitReplenishment }}</div>
        </div> -->
        <div class="list">
          <div class="side-right">正在拣货的整件任务数：</div>
          <div class="side-right">{{ picking }}</div>
        </div>
        <!-- <div class="list">
          <div class="side-right">正在拣货的补货任务数：</div>
          <div class="side-right">{{ replenishment }}</div>
        </div> -->
      </div>
      <!-- 表格 -->
      <div v-table class="table-box">
        <vxe-table ref="xTable" :loading="loading" highlight-current-row height="488px" :data="tableData"
          :sort-config="{ remote: true }" @sort-change="sortHandler" :row-class-name="rowClassName"
          @cell-click="currentChangeEvent" @checkbox-change="checkboxChangeEvent" resizable
          @resizable-change="resizableChange" :columns="columns" :key="tableKey">
          <vxe-column type="checkbox" width="60"></vxe-column>
          <vxe-table-column type="seq" title="序号" width="80" />
          <template v-for="item in columns">
            <vxe-table-column :key="item.field" :field="item.field" :title="item.title" :min-width="item.width"
              :sortable="item.sortable" v-if="item.visible" />
          </template>
        </vxe-table>
      </div>
      <!-- <div class="pager">
        <vxe-pager border :current-page="tablePage.pageNum" :page-size="tablePage.pageSize" :total="tablePage.total"
          :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
          @page-change="handlePageChange" />
      </div> -->
    </el-card>
    <!-- 订单详情 -->
    <order-detail ref="orderDetail" />
    <div>
      <!--筛选列组件导入-->
      <filter-table-head ref="filterDialog" @confirm="setFilterTableHead" @reset="resetFilterTableHead" />
    </div>
    <statistics :visable="statisticsVisable" :propsData="statisticsData" />
    <retroactiveCodeDialog ref="retroactiveCodeDialog" @dialog-close="dialogClose" @refresh="search"
      @staticSearch="getWholeTaskStatistics" />
  </div>
</template>
<script>
//导入保存和查询自定义列接口 
import { queryUserCustomColumn, saveUserCustomColumn } from '@/api/public.js'
// import utils from '@/utils'
import { queryWholePickingList, queryWholeTaskStatistics, printPickingList, findEmpNumber, queryWholeTask, queryAgvTask, queryScanTask, confirmPicking, printPickingLabel } from '@/api/outstock/index.js'
import { columns } from './colums.js'
import OrderDetail from './detailModal'
import { printNew } from '@/utils/print.js'
import Statistics from "./statistics"
import retroactiveCodeDialog from './components/retroactiveCodeDialog.vue'
export default {
  name: 'gettingTask',
  components: { OrderDetail, Statistics, retroactiveCodeDialog },
  data() {
    return {
      btnList: [
        {
          label: 'AGV索取',
          type: 'warning',
          icon: '',
          clickEvent: this.queryAgvTask,
          code: "btn:wms:gettingTask:search",
        },
        {
          label: '查询任务',
          type: 'primary',
          icon: 'el-icon-search',
          clickEvent: this.search,
          code: "btn:wms:gettingTask:search",
        },
        {
          label: '索取任务',
          type: 'primary',
          icon: "el-icon-share",
          clickEvent: this.queryWholeTask,
          code: "btn:wms:gettingTask:queryWholeTask",
        },
        {
          label: '拣货确认',
          type: 'success',
          icon: "el-icon-check",
          clickEvent: this.pickConfirm,
          code: "btn:wms:gettingTask:pickConfirm",
        },
        // {
        //   label: '检查拣货员工号',
        //   type: 'primary',
        //   icon: 'el-icon-check',
        //   clickEvent: this.checkEmployeeNumber
        // },
        {
          label: '重打标签',
          type: 'warning',
          icon: "el-icon-printer",
          clickEvent: this.rePrintTag,
          code: "btn:wms:gettingTask:rePrintTag",
        },
        {
          label: '重打拣货单',
          type: 'warning',
          icon: "el-icon-printer",
          clickEvent: this.retypePickList,
          code: "btn:wms:gettingTask:retypePickList",
        },
      ],
      formData: {
        employeeNumber: "E",
        orderCode: '',
        productCode: '',
        highLevel: "",
        sortname: "",
        sorttype: "",
        isPrintWholePicking: null,
        isPrintWholePickingTag: 1
      },
      isPrintWholePicking: false,
      isPrintWholePickingTag: true,
      highLevel: [{
        value: '1',
        label: '是'
      }, {
        value: '0',
        label: '否'
      }],
      tweenMessage: `
          <div>单据编号单据编号单据编号单据编号单据编号</div>
        `,
      tweenWidth: 500,
      stopBoole: 1,
      fristMove: true,
      loading: false,
      tablePage: {
        pageNum: 1,
        pageSize: 100,
        total: 0
      },
      columns: columns(),
      oldColumns: JSON.parse(JSON.stringify(columns())),
      tableKey: Date.now(), //表格列刷新flag
      storeDone: true, //后端是否存在自定义列宽数据 true->存在 false->不存在
      tableData: [], // 表格数据
      statisticsVisable: false,  //统计弹窗
      statisticsData: [],
      data: [],
      finisedInReviewList: '0',
      unBindGoodOrderRequestedList: '0',
      bindGoodOrderRequestedList: '0',
      beltCollection: '0',
      picking: '0',
      replenishment: '0',
      emergencyReplenishmentList: '0',
      passiveReplenishmentList: '0',
      batchReplenishmentList: '0',
      capacityReplenishmentList: '0',
      waitReplenishment: '0',
      jobName: '',
    }
  },
  // mounted() {
  //   this.tweenWidth = this.$refs.rowMessage.offsetWidth;
  //   this.search()
  //   this.getWholeTaskStatistics()
  // },
  activated() {
    // this.tweenWidth = this.$refs.rowMessage.offsetWidth;
    this.search()
    this.getWholeTaskStatistics()
    this.$nextTick(() => {
      // utils.pageActivated()
      this.getColumWidth('gettingTask', 'columns', 'xTable')
    })
  },
  methods: {
    // 设置表头筛选列-子组件回传
    setFilterTableHead({ type, fullColumns, tableNo }) {
      this.columns = [...fullColumns]
      this.tableKey = Date.now()
      this.setColumnWidth('gettingTask', 'columns')
      this.$nextTick(() => {
        this.getColumWidth('gettingTask', 'columns', 'xTable')
      })
    },
    //设置筛选列
    setingTableDataHander(index) {
      // this.type = index;
      let columns = [];
      columns = JSON.parse(JSON.stringify((this.columns)));
      this.$refs.filterDialog.open(columns, 1, true, 'xTable')
    },
    //重置自定义列宽方法
    //每一个vxe-table单独实现
    resetFilterTableHead(tableNo) {
      this.oldColumns = JSON.parse(JSON.stringify((columns())));
      this.columns = [...this.oldColumns]
      this.tableKey = Date.now()
      this.setColumnWidth('gettingTask', 'oldColumns')
      this.$nextTick(() => {
        this.getColumWidth('gettingTask', 'columns', 'xTable')
      })
    },
    //监测拖动列宽变化方法
    resizableChange({ column }) {
      this.columns[this.columns.findIndex(item => item.title === column.title)].width = column.resizeWidth
    },

    //保存/更新自定义列方法实现
    //page: 自定义列的唯一标识 多为该单页routerName 单页存在多个表格则在此做变化
    // column： 所需要保存的表格绑定的列数据
    setColumnWidth(page, column) {
      const columns = this[column]
      const params = {
        page: page,
        columns: columns
      }
      saveUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          this.$message.success(msg)
        } else {
          this.$message.error(msg)
        }
      })
    },
    //查询自定义列方法实现
    // page:保存时所给的列唯一标识 一般是该单页的routerName 单页存在多个表格则每个表格的标识不同
    // column:所需要渲染的表格绑定的列数据
    // table: 所需要渲染的表格的 'ref'
    getColumWidth(page, column, table) {
      const params = {
        page: page,
      }
      queryUserCustomColumn(params).then(res => {
        const { code, msg, result } = res
        if (code === 0 && result) {
          const columns = result
          //出参只含field和width的匹配
          columns?.forEach(item => {
            //更改对应column的
            this[column][this[column].findIndex(d => d.field === item.field)].width = item.width
            //匹配后端所传列顺序
            const foundItem = this[column].find(d => d.field === item.field)
            if (foundItem) {
              this[column].push(foundItem)
              this[column].splice(this[column].indexOf(foundItem), 1)
            }
          })
          this.storeDone = true //查询到列表格数据标识
          this.tableKey = Date.now() //强制刷新表格列
          // 若返回数据格式存在 field,title,visible,width
          // this[column] = columns
          // this.tableKey = Date.now() 
          // this.storeDone = true
        } else {
          this.storeDone = false
        }
      })
    },
    rePrintTag() {
      const selectedRows = this.$refs.xTable.getCheckboxRecords();
      if (selectedRows.length === 0) {
        this.$message.warning('请选择要重打标签的数据')
        return
      }
      for (let i = 0; i < selectedRows.length; i++) {
        if (!selectedRows[i].tagCode) {
          this.$message.warning('请输入索取人员工号')
          return
        }
      }
      const tagCodeList = selectedRows.map(item => item.tagCode)
      const params = Object.assign({ tagCodeList: tagCodeList })
      printPickingLabel(params).then(res => {
        const { code, msg, result } = res
        if (code === 0) {
          printNew(result)
          // this.$message.success(msg)
        } else {
          this.$message.error(msg)
        }
      })
    },
    /**权限控制 */
    getPermission(code) {
      if (!this.$route.meta.buttonList) {
        return false;
      }
      const permissions = this.$route.meta.buttonList.map(item => {
        return item.code;
      });
      return permissions.indexOf(code) !== -1
    },
    async search() {
      await this.getList();
    },
    // 列表查询
    async getList() {
      const params = Object.assign(this.formData);
      this.loading = true;
      try {
        const res = await queryWholePickingList(params);
        const { code, msg, result } = res;
        if (code === 0) {
          this.tableData = result || [];
        } else {
          this.tableData = [];
          this.$message.error(msg);
        }
      } catch (error) {
        console.error('获取列表失败:', error);
        this.$message.error('获取列表时发生错误');
      }
      this.loading = false;
    },
    // 分页器改变处理
    // handlePageChange({ currentPage, pageSize }) {
    //   this.tablePage.pageNum = currentPage
    //   this.tablePage.pageSize = pageSize
    //   this.getList()
    // },
    // //设置筛选列
    // setingTableDataHander() {
    //   const columns = JSON.parse(JSON.stringify((this.columns)));
    //   this.$refs.filterDialog.open(columns, 1)
    // },
    // // 设置表头列-子组件回传
    // setFilterTableHead({ type, fullColumns }) {
    //   this.columns = fullColumns;
    // },
    // // 设置表头列-子组件回传
    // setFilterTableHead2({ type, fullColumns }) {
    //   this.columns = fullColumns;
    // },
    //排序
    sortHandler({ data, column, field, order }) {
      if (column.property == 'createTime') {
        if (order == 'asc') {
          this.formData.sortname = column.property
          this.formData.sorttype = order
          this.tableData.sort((a, b) => {
            return new Date(a[column.property].replace(/-/g, '/')) - new Date(b[column.property].replace(/-/g, '/'))
          })
        } else if (order == 'desc') {
          this.formData.sortname = column.property
          this.formData.sorttype = order
          this.tableData.sort((a, b) => {
            return new Date(b[column.property]) - new Date(a[column.property])
          })
          this.formData.sortname = column.property
          this.formData.sorttype = order
        } else {
          this.formData.sortname = ''
          this.formData.sorttype = ''
        }
      }
      if (column.property == 'occupyOrders') {
        if (order == 'asc') {
          this.formData.sortname = column.property
          this.formData.sorttype = order
          this.tableData.sort((a, b) => {
            return a[column.property] - b[column.property]
          })
        } else if (order == 'desc') {
          this.formData.sortname = column.property
          this.formData.sorttype = order
          this.tableData.sort((a, b) => {
            return b[column.property] - a[column.property]
          })
        } else {
          this.formData.sortname = ''
          this.formData.sorttype = ''
        }
      }
      if (column.property == 'soldOut') {
        if (order == 'asc') {
          this.formData.sortname = column.property
          this.formData.sorttype = order
          this.tableData.sort((a, b) => {
            return a[column.property] - b[column.property]
          })
        } else if (order == 'desc') {
          this.formData.sortname = column.property
          this.formData.sorttype = order
          this.tableData.sort((a, b) => {
            return b[column.property] - a[column.property]
          })
        } else {
          this.formData.sortname = ''
          this.formData.sorttype = ''
        }
      }
      this.search()
    },
    checkPickChange() {
      if (this.isPrintWholePicking) {
        this.formData.isPrintWholePicking = 1
      } else {
        this.formData.isPrintWholePicking = 0
      }
    },
    checkPickTagChange() {
      if (this.isPrintWholePickingTag) {
        this.formData.isPrintWholePickingTag = 1
      } else {
        this.formData.isPrintWholePickingTag = 0
      }
    },
    //统计打开关闭
    openStatistics() {
      this.statisticsVisable = true
    },
    clostStatistics() {
      this.statisticsVisable = false
    },
    async queryWholeTask() {
      const selectedRows = this.$refs.xTable.getCheckboxRecords();
      if (selectedRows.length == 0) {
        this.$message.warning('请选择要索取的整件任务');
        return;
      }

      await this.entryJobNo(false);

      // 等待输入的员工号和作业名称有效
      if (this.formData.employeeNumber == '' || this.jobName == '') {
        return;
      }

      let idsArr = selectedRows.map(item => parseInt(item.id));  
      const idList = idsArr;
      const { employeeNumber, isPrintWholePicking, isPrintWholePickingTag } = this.formData;

      const params = { idList, employeeNumber, isPrintWholePicking, isPrintWholePickingTag };

      try {
        const res = await queryWholeTask(params);
        const { code, msg, result } = res;
        if (code === 0) {
          this.$message.success(msg);
          if (result.isPrint === 1) {
            printNew(result.printContent);
          }
          await this.search();
          await this.handleCheckboxRow();
        } else {
          this.$message.error(msg);
        }
      } catch (error) {
        console.error('查询任务失败:', error);
        this.$message.error('查询任务时发生错误');
      }
    },
    //agv任务索取
    async queryAgvTask(){
      const selectedRows = this.$refs.xTable.getCheckboxRecords();
      if (selectedRows.length == 0) {
        this.$message.warning('请选择要索取的整件任务');
        return;
      }else if(selectedRows.length>1){
        this.$message.warning('只能选择一条数据');
        return;
      }

      await this.entryJobNo(false);

      // 等待输入的员工号和作业名称有效
      if (this.formData.employeeNumber == '' || this.jobName == '') {
        return;
      }

      let idsArr = selectedRows.map(item => parseInt(item.id));  
      const idList = idsArr;
      const { employeeNumber, isPrintWholePicking, isPrintWholePickingTag } = this.formData;

      const params = { idList, employeeNumber, isPrintWholePicking, isPrintWholePickingTag };

      try {
        const res = await queryAgvTask(params);
        const { code, msg, result } = res;
        if (code === 0) {
          this.$message.success(msg);
          if (result.isPrint === 1) {
            printNew(result.printContent);
          }
          await this.search();
          await this.handleCheckboxRow();
        } else {
          this.$message.error(msg);
        }
      } catch (error) {
        console.error('查询任务失败:', error);
        this.$message.error('查询任务时发生错误');
      }
    },
    async pickConfirm() {
      const selectedRows = this.$refs.xTable.getCheckboxRecords();
      if (selectedRows.length <= 0) {
        this.$message.error("请选择要处理的数据");
        return;
      }

      // 检查任务状态
      for (let i = 0; i < selectedRows.length; i++) {
        if (selectedRows[i].orderStatus != 310) {
          this.$message.warning("该任务未索取,无法拣货!");
          return;
        }
      }

      // 确保员工号和作业名称有效
      await this.entryJobNo(false);
      if (this.jobName == '' || this.formData.employeeNumber == '') {
        return;
      }

      // 构造参数
      let idsArr = selectedRows.map(item => parseInt(item.id));
      const ids = idsArr;
      const employeeNumber = this.formData.employeeNumber;
      const params = { ids, employeeNumber };

      try {
        // 查询扫描任务
        const scanRes = await queryScanTask({ batchInspectionCode: selectedRows[0].batchInspectionCode });
        const { code, msg, result } = scanRes;

        if (code === 0) {
          if (result && result.length > 0) {
            this.$refs.retroactiveCodeDialog.open(result, employeeNumber);
          } else {
            // 如果没有扫描任务，确认拣货
            const confirmRes = await confirmPicking(params);
            const { code, msg, result } = confirmRes;
            if (code === 0) {
              this.$message.success(msg);
              await this.search();  // 查询列表
              await this.getWholeTaskStatistics();  // 获取任务统计
            } else {
              this.$message.error(msg);
            }
          }
        } else {
          this.$message.error(msg);
        }
      } catch (error) {
        console.error('处理任务失败:', error);
        this.$message.error('处理任务时发生错误');
      }
    },

    retypePickList() {
      if (this.selectData) {
        const { row } = this.selectData
        const employeeNumber = row.employeeNumber
        const params = { employeeNumber }
        printPickingList(params).then(res => {
          const { code, msg, result } = res
          if (code === 0) {
            printNew(result)
          } else {
            this.$message.error(msg)
          }
        })
      } else {
        this.$message.error('请先选中一行后再操作！')
      }
    },
    async handleCheckboxRow() {
      // 等待更新复选框状态和获取统计数据
      await this.$nextTick();
      this.$refs.xTable.setAllCheckboxRow(true);
      this.getWholeTaskStatistics();
    },
    async entryJobNo(needSearch) {
      await this.checkEmployeeNumber(needSearch);
    },
    async checkEmployeeNumber(needSearch) {
      if (!this.formData.employeeNumber) {
        this.$message.error('请输入拣货员工号');
        return;
      }
      this.jobName = '';
      const params = { employeeNumber: this.formData.employeeNumber };

      try {
        const res = await findEmpNumber(params);
        const { code, msg, result } = res;
        if (code === 0) {
          this.jobName = result ? result : '';
          if (needSearch) {
            await this.search();
          }
        } else {
          this.$message.error(msg);
        }
      } catch (error) {
        console.error('查询员工号失败:', error);
        this.$message.error('查询员工号时发生错误');
      }
    },
    async getWholeTaskStatistics() {
      try {
        const res = await queryWholeTaskStatistics();
        const { code, msg, result } = res;
        if (code === 0 && result) {
          this.data = result || [];
          this.dataToString();
        } else {
          this.data = [];
          this.$message.error(msg);
        }
      } catch (error) {
        console.error('获取任务统计失败:', error);
        this.$message.error('获取任务统计时发生错误');
      }
    },
    dataToString() {
      if (this.data.finisedInReviewList != null) {
        this.finisedInReviewList = this.data.finisedInReviewList.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.unBindGoodOrderRequestedList != null) {
        this.unBindGoodOrderRequestedList = this.data.unBindGoodOrderRequestedList.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.bindGoodOrderRequestedList != null) {
        this.bindGoodOrderRequestedList = this.data.bindGoodOrderRequestedList.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.beltCollection != null) {
        this.beltCollection = this.data.beltCollection.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.picking != null) {
        this.picking = this.data.picking.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.replenishment != null) {
        this.replenishment = this.data.replenishment.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.emergencyReplenishmentList != null) {
        this.emergencyReplenishmentList = this.data.emergencyReplenishmentList.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.passiveReplenishmentList != null) {
        this.passiveReplenishmentList = this.data.passiveReplenishmentList.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.batchReplenishmentList != null) {
        this.batchReplenishmentList = this.data.batchReplenishmentList.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.capacityReplenishmentList != null) {
        this.capacityReplenishmentList = this.data.capacityReplenishmentList.map(item => `${item.area}区:${item.count}`).join(',')
      }
      if (this.data.waitReplenishment != null) {
        this.waitReplenishment = this.data.waitReplenishment.map(item => `${item.area}区:${item.count}`).join(',')
      }
    },
    currentChangeEvent(val) {
      this.selectData = val
      this.$refs.xTable.toggleCheckboxRow(val.row)
    },
    rowClassName(row, rowlndex, $rowlndex) {
      if (row.row.orderSort == 1) {
        return 'attach-lable';
      } else {
        return ''
      }
    },
    //弹窗关闭
    dialogClose() {

    },
    // //索取
    // findEmpNumber() {
    //   if (!this.formData.employeeNumber) {
    //     this.$message.error("请输入拣货员工号")
    //     return
    //   }
    //   const params = {
    //     employeeNumber: this.formData.employeeNumber
    //   }
    //   findEmpNumber(params).then(res => {
    //     if (code === 0) {
    //       //索取
    //       queryWholeTask().then(res => {
    //         if (code === 0) {
    //           this.$message.success(msg)
    //           this.getList()
    //         } else {
    //           this.$message.error(msg)
    //         }
    //       })
    //     } else {
    //       this.$message.error(msg)
    //     }
    //   })
    // }
    checkboxChangeEvent({ row }) {
      this.$refs.xTable.toggleCheckboxRow(row)
    }
  }
}
</script>

<style lang="scss" scoped>
.heade-line {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}

.heade-line .title {
  width: 10%;
}

.row-message-class {
  display: inline-block;
  width: 700px;
  margin-left: 10px;
  margin-top: 5px;
}

.setField {
  margin-left: auto;
}

.summary .list {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 5px;
}

.statisticsBtn {
  position: absolute;
  top: 21px;
  right: 36px;
}

.table-box {
  height: 512px;
}
</style>
