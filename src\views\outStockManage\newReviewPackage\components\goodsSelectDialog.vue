<template>
  <div>
    <xyy-dialog
      ref="dialogTableVisible"
      title="商品列表"
      width="40%"
      :visible.sync="outerVisible"
      @on-close="closeDialog"
    >
      <!-- 商品列表表格 -->
      <vxe-table
        ref="goodsTable"
        highlight-current-row
        highlight-hover-row
        height="300px"
        class="goods-table"
        :loading="loading"
        :data="goodsData"
        @cell-dblclick="handleCellClick"
      >
        <vxe-table-column type="seq" title="序号" width="80" />
        <vxe-table-column field="productCode" title="商品编码" width="200" />
        <vxe-table-column field="productName" title="商品名称" width="200" />
        <vxe-table-column field="batchNumber" title="批号" width="245" />
      </vxe-table>
    </xyy-dialog>
  </div>
</template>

<script>
import { getExceptionGoodsList } from '@/api/outstock/fhdb';

export default {
  name: "GoodsSelectDialog",
  data() {
    return {
      show: false,
      loading: false, // 列表懒加载
      goodsData: [], // 商品数据
      outerVisible: false,
    };
  },
  methods: {
    // 关闭弹窗
    close() {
      this.$refs.dialogTableVisible.close();
    },
    
    // 打开弹窗
    open(selectedData, packageBarCode) {
      this.loading = true;
      
      // 获取商品数据
      getExceptionGoodsList({ packageBarCode }).then(res => {
        this.loading = false;
        const { code, msg, result } = res;
        
        // 处理已选择的数据，过滤重复项
        const singleArray = [];
        if (selectedData && selectedData.length >= 0) {
          selectedData.forEach(e => {
            const itemObj = {
              batchNumber: e.batchNumber,
              productCode: e.productCode
            };
            singleArray.push(itemObj);
          });
        }
        
        if (code === 0) {
          // 数据过滤：已选的数据不在返回的数据中显示
          if (result && result.length > 0) {
            const localData = result.filter(item => 
              !singleArray.some(data => 
                data.productCode === item.productCode && 
                data.batchNumber === item.batchNumber
              )
            );
            this.goodsData = localData;
          } else {
            this.goodsData = [];
          }
        } else {
          this.$message.error(msg);
          this.goodsData = [];
        }
      }).catch(() => {
        this.loading = false;
        this.goodsData = [];
      });
      
      this.$refs.dialogTableVisible.open();
    },
    
    // 数组去重工具方法
    unique(arr) {
      return arr.filter(function(item, index, arr) {
        // 当前元素，在原始数组中的第一个索引==当前索引值，否则返回当前元素
        return arr.indexOf(item, 0) === index;
      });
    },
    
    // 点击当前行（双击选择）
    handleCellClick(row) {
      // 改变数据，标记为结果商品，设置异常原因为多货
      const rowChanged = Object.assign(row.row, {
        "isResultGoods": true,
        "exceptionCause": "2"
      });
      
      // 发送事件给父组件
      this.$emit('on-goods-selected', rowChanged);
      
      // 删除该条数据（避免重复选择）
      this.goodsData.splice(this.goodsData.indexOf(row.row), 1);
      
      // 关闭弹窗
      this.$refs.dialogTableVisible.close();
    },
    
    // 重新回写从异常列表删除的数据
    overWriteGoodsData(params) {
      if (params && params.length >= 0) {
        this.goodsData = [];
        params.forEach(e => {
          this.goodsData.push(e);
        });
      }
    },
    
    // 禁止空白关闭弹窗
    closeDialog() {
      this.$emit('on-close');
    }
  },
};
</script>

<style lang="scss" scoped>
.goods-table {
  :deep(.vxe-header--column) {
    background-color: #f5f7fa;
    font-weight: 600;
  }
  
  :deep(.vxe-body--row:hover) {
    background-color: #f0f9ff;
    cursor: pointer;
  }
  
  :deep(.vxe-body--row:active) {
    background-color: #e6f7ff;
  }
}
</style>
