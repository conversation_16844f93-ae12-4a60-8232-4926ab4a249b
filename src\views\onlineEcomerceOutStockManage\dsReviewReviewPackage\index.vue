<template>
    <div class="app-container">
        <div class="el-card">
            <!-- 按钮组 -->
            <btn-group :btn-list="btnList" style="float: right; margin-left: 11px;" />
            <div class="header-right">
                <label>复核员：</label>
                <span id="t_name">{{ storageTool.getNickName() }}</span>
            </div>
        </div>
        <el-container>
            <!-- 左侧数据列表 -->
            <el-aside width="570px">
                <!-- table 组件 -->
                <vxe-table ref="reviewTable" highlight-hover-row height="780" class="review-table" :loading="loading"
                    :data="tableData" :row-class-name="rowClassName" @cell-click="cellClickRow" stripe resizable>
                    <!-- @cell-dblclick="cellDBLClickRow" -->
                    <vxe-table-column type="seq" title="操作" fixed="left" width="120">
                        <template v-slot="{ row }">
                            <vxe-button v-if="row.reviewStatus === 0" status="primary"
                                @click="reviewClick(row)">复核</vxe-button>
                            <vxe-button v-else status="primary" @click="unReview(row)">取消复核</vxe-button>
                        </template>
                    </vxe-table-column>
                    <vxe-table-column type="checkbox" width="40" />
                    <vxe-table-column type="seq" title="序号" width="80" />
                    <vxe-table-column field="productName" title="商品名称" width="120" />
                    <vxe-table-column field="batchNumber" title="批号" width="200" />
                    <vxe-table-column field="reviewPartsNumber" title="复核数量件数" width="180" />
                    <vxe-table-column field="productCode" title="商品编码" width="200" />
                    <vxe-table-column field="barCode" title="条形码" width="200" />
                    <vxe-table-column field="reviewStatus" title="内复核状态" width="120" :formatter="reviewFormatter" />
                    <vxe-table-column field="allocationCode" title="分配单号" width="200" />

                    <vxe-table-column field="mergeOrderCode" title="出库单号" width="200" />
                    <vxe-table-column field="erpOrderCode" title="销售单号" width="200" />
                    <vxe-table-column field="packagingRequirement" title="包装要求" width="180" />
                    <vxe-table-column field="productDateRequire" title="生产日期要求" width="120" />
                    <vxe-table-column field="mnemonicCode" title="商品助记码" width="120" />
                    <vxe-table-column field="specification" title="件包装" width="120" />
                    <vxe-table-column field="reviewNumber" title="复核数量" width="120" />

                    <vxe-table-column field="goodsAllocation" title="显示货位" width="120" />
                    <vxe-table-column field="pickingNumber" title="计划零散数" width="120" />
                    <vxe-table-column field="sterilizingBatchNumber" title="灭菌批号" width="200" />
                    <vxe-table-column field="produceTime" title="生产日期" :formatter="timeFormatter" width="150" />
                    <vxe-table-column field="validDate" title="有效期至" :formatter="timeFormatter" width="150" />
                    <vxe-table-column field="realPickingNumber" title="实际数量" width="120" />
                    <vxe-table-column field="realPickingNumber2" title="拣货数量件数" width="120" />
                    <vxe-table-column field="whetherRegulatory" title="是否监管" width="120" :formatter="isFormatter" />
                    <vxe-table-column field="acceptancePoints" title="验收注意点" width="120" />

                    <vxe-table-column field="packingUnit" title="包装单位" width="120" />
                    <vxe-table-column field="manufacturer" title="生产厂家" width="120" />
                    <vxe-table-column field="specifications" title="规格" width="150" />
                    <vxe-table-column field="producingArea" title="产地" width="120" />

                    <vxe-table-column field="dosageFormName" title="剂型" width="120" />
                    <vxe-table-column field="mediumPacking" title="中包装" width="120" />
                    <vxe-table-column field="largePacking" title="件包装" width="120" />
                    <vxe-table-column field="maxMediumPacking" title="最大中包装" width="120" />
                    <vxe-table-column field="maxLargePacking" title="最大件包装" width="120" />
                    <vxe-table-column field="isPrecious" title="是否贵重药品" width="120" :formatter="isFormatter" />
                    <vxe-table-column field="isFragile" title="是否易碎" width="120" :formatter="isFormatter" />
                    <vxe-table-column field="storageAttributes" title="商品特殊属性" width="120" />
                    <vxe-table-column field="liquid" title="含有液体" width="120" />
                </vxe-table>
            </el-aside>
            <!-- 中间 商品信息详情 -->
            <el-main height="780px">
                <el-form ref="formData" :model="formData">
                    <el-col :lg="24" :md="24">
                        <el-form-item label="商品名称：" prop="productName" label-width="82px">
                            <span class="large-size">{{ formData.productName }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="24" :md="24">
                        <el-form-item label="规格：" prop="specifications" label-width="82px">
                            <span class="medium-size">{{ formData.specifications }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="24" :md="24">
                        <el-form-item label="批号：" prop="batchNumber" label-width="82px">
                            <span class="large-size">{{ formData.batchNumber }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="24" :md="24">
                        <el-form-item label="生产日期：" prop="produceTime" label-width="82px">
                            <span class="medium-size">{{ formData.produceTime }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="24" :md="24">
                        <el-form-item label="有效期至：" prop="validDate" label-width="82px">
                            <span class="medium-size">{{ formData.validDate }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="24" :md="24">
                        <el-form-item label="复核数量：" prop="reviewNumber" label-width="82px">
                            <span class="medium-size">{{ formData.reviewNumber }}{{ formData.packingUnit }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="24" :md="24">
                        <el-form-item label="特殊属性：" prop="specialAttributes" label-width="82px">
                            <span class="medium-size">{{ formData.specialAttributes }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="24" :md="24">
                        <el-form-item label="生产厂家：" prop="manufacturer" label-width="82px">
                            <span class="medium-size">{{ formData.manufacturer }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="16" :md="16">
                        <el-form-item label="包装要求：" prop="packagingRequirement" label-width="82px">
                            <span class="medium-size">{{
                                formData.packagingRequirement
                            }}</span>
                        </el-form-item>
                        <el-form-item label="中包装数：" prop="mediumPacking" label-width="82px">
                            <span class="medium-size">{{ formData.mediumPacking }}</span>
                        </el-form-item>
                    </el-col>
                    <el-col :lg="8" :md="8">
                        <el-image :src="formData.src">
                            <div slot="placeholder" class="image-slot">
                                加载中<span class="dot">...</span>
                            </div>
                        </el-image>
                    </el-col>
                </el-form>
            </el-main>
            <!-- 右侧 组件 -->
            <el-aside class="form-right" width="420px">
                <el-form :model="formRight">
                    <el-form-item label="面单号：" prop="trackingNumber" label-width="82px">
                        <el-input ref="trackingNumber" v-model="formRight.trackingNumber" placeholder="请输入面单号"
                            onfocus="this.select()" @keyup.enter.native="scanOrder" />
                    </el-form-item>
                    <el-form-item label="耗材码：" prop="boxCode" label-width="82px">
                        <!-- 扫描面单号自动带出 -->
                        <el-input ref="boxCode" v-model="formRight.boxCode" placeholder="请输入耗材码："
                            onfocus="this.select()" @keyup.enter.native="boxCodeEnther" />
                    </el-form-item>
                    <el-form-item label="商品条码：" prop="barCode" label-width="82px">
                        <el-input ref="barCode" v-model="formRight.barCode" placeholder="请输入商品条码"
                            onfocus="this.select()" @keyup.enter.native="barCodeEnter" />
                    </el-form-item>

                    <!-- 商品总数量 -->
                    <div class="head-title">商品总数量</div>
                    <div>
                        <label class="item-label">总计：</label>
                        <span class="item-number">{{ formRight.productNumberAll }}</span>
                    </div>
                    <div>
                        <label class="item-label">已检：</label>
                        <span class="item-number">{{ formRight.productNumber }}</span>
                    </div>
                    <!-- 品种数 -->
                    <div class="head-title">品种数</div>
                    <div>
                        <label class="item-label">总计：</label>
                        <span class="item-number">{{
                            formRight.inreviewProductNumberAll
                        }}</span>
                    </div>
                    <div>
                        <label class="item-label">已检：</label>
                        <span class="item-number">{{
                            formRight.inreviewProductNumber
                        }}</span>
                    </div>
                </el-form>
                <div v-table class="table-box">
                    <div style="border-top: 1px solid lightgray; font-size: 22px; margin-top: 22px;">耗材</div>
                    <vxe-table ref="rTable" height="125" :data="boxTableData">
                        <vxe-table-column type="seq" title="序号" width="80" />
                        <vxe-table-column field="boxCode" title="耗材码" min-width="100" />
                        <vxe-table-column field="boxType" title="耗材名称" min-width="100" />
                        <vxe-table-column field="operation" title="操作" width="95">
                            <template #default="{ row }">
                                <el-button type="text" @click="deleteBox(row)">删除</el-button>
                            </template>
                        </vxe-table-column>
                    </vxe-table>
                </div>
            </el-aside>
        </el-container>
        <!-- 追溯码扫描 -->
        <tracing-code-scan ref="tracingCodeScanDialog" @on-before-close="closeDialog"
            @regulatoryCodeScanBack="changeTracing" />
        <!-- 查看任务弹窗 -->
        <view-tasks ref="viewTasksDialog" @on-before-close="closeViewTasksDialog" />
    </div>
</template>

<script>
import {
    checkParts,
} from "@/api/outstock/fhdb";
import { doTTS } from "@/utils/tts.js";
import ViewTasks from "./components/viewTasks"; // 查看任务
import tracingCodeScan from "./components/tracingCodeScan"; // 追溯码扫描
import {
    getPartsInReviewGoodsSalesInfo,
    listPartsInReviewGoodsView,
    saveBoxCode,
    review,
    reviewConfirm,
} from "@/api/stockOutOnline/fhdbToC";
import XEUtils from "xe-utils"; // 表格组件工具
import axios from "axios";
export default {
    name: "ReviewPackage",
    components: { ViewTasks, tracingCodeScan },
    data() {
        return {
            intervalId: undefined,
            btnList: [
                {
                    label: "查看任务",
                    type: "primary",
                    shortkey: "F4",
                    clickEvent: this.searchBtn,
                    code: 'btn:wms:dsReviewReviewPackage:search'
                },
                {
                    label: "复核确认",
                    type: "primary",
                    shortkey: "F7",
                    clickEvent: this.reviewBtn,
                    code: 'btn:wms:dsReviewReviewPackage:review'
                },
            ],
            regulatoryCodeVOs: {},
            // 表单数据:作为主表单上传的数据
            formData: {
                productName: "", // 商品名称
                specifications: "", // 规格
                batchNumber: "", // 批号
                produceTime: "", // 生产日期
                validDate: "", // 有效期至
                reviewNumber: "", // 复核数量
                packingUnit: "", // 单位
                specialAttributes: "", // 特殊属性
                manufacturer: "", // 生产厂家
                packagingRequirement: "", // 包装要求
                mediumPacking: "", // 中包装数量
                src: require("@/assets/images/product.png"),
            },
            printStatus: false,
            batchInspectionCode: "", // 批件单号
            formRight: {
                orderCode: "", //出库单号
                consolidationCode: undefined, //拼箱号，如果有拼箱号，需要扫描耗材码
                allocationCode: "", // 分配单号
                boxCode: "", // 小车号
                trackingNumber: "", // 面单号
                barCode: "", // 商品条码
                productNumberAll: 0, // 商品总数量
                productNumber: 0, // 商品已检数量
                inreviewProductNumberAll: 0, // 品种数总数量
                inreviewProductNumber: 0, // 品种已检数
                supervisoryCodeAcquisition: "", // 电子监管码采集
            },
            isAutomaticPrint: 0, // 是否扫描自动打印
            isFaceOrderPrint: 0, // 是否面单打印
            isInventoryPrint: 0, // 是否清单打印
            hotkeys: "F4,F7,F10", // 页面快捷键
            tableData: [], // 表格数据
            abnormalTableData: [], // 异常提交数据
            rowIndex: "", // 上次选中的行
            selectData: null, // 选中数据
            hasDialog: false, // 弹窗禁止操作快捷键
            loading: false, // 表格加载层
            isWhetherRegulatory: false, // 是否打开监管码弹窗
            inNum: 0,
            videoId: "-1",
            boxTableData: [],
            checkRow: "",
        };
    },
    // mounted() { },
    // created() {
    //     // 初始化时：默认面单号获取焦点
    //     this.$nextTick(() => {
    //         this.$refs.trackingNumber.focus();
    //     });
    //     this.loading = true; // 加载中
    //     setTimeout(() => {
    //         this.loading = false;
    //     }, 1000);
    //     // 绑定快捷键
    //     this.$hotkeys(this.hotkeys, (event, handler) => {
    //         if (this.hasDialog) {
    //             return false;
    //         }
    //         event.preventDefault(); // 阻止浏览器默认行为
    //         switch (handler.key) {
    //             case "F4":
    //                 this.searchBtn();
    //                 break;
    //             case "F7":
    //                 this.reviewBtn();
    //                 break;
    //         }
    //     });
    // },
    activated() {
        // 初始化时：默认面单号获取焦点
        this.$nextTick(() => {
            this.$refs.trackingNumber.focus();
        });
        this.loading = true; // 加载中
        setTimeout(() => {
            this.loading = false;
        }, 1000);
        // 绑定快捷键
        this.$hotkeys(this.hotkeys, (event, handler) => {
            if (this.hasDialog) {
                return false;
            }
            event.preventDefault(); // 阻止浏览器默认行为
            switch (handler.key) {
                case "F4":
                    this.searchBtn();
                    break;
                case "F7":
                    this.reviewBtn();
                    break;
            }
        });
    },
    beforeDestroy() {
        // 销毁快捷键
        this.$hotkeys.unbind(this.hotkeys);
        clearInterval(this.intervalId);
    },
    methods: {
        timeFormatter({ cellValue, row, column }) {
            return XEUtils.toDateString(cellValue, "yyyy-MM-dd");
        },
        isFormatter({ cellValue, row, column }) {
            let value = cellValue === 0 ? "否" : "是";
            return value;
        },
        reviewFormatter({ cellValue, row, column }) {
            let value = "";
            if (cellValue === 0) {
                value = "待复核";
            } else if (cellValue === 1) {
                value = "复核中";
            } else {
                value = "已复核";
            }
            return value;
        },
        // 单击选中行事件
        cellClickRow({ rowIndex, row }) {
            this.checkRow = row;
            this.setReviewDetails(row);
        },
        // 列复核事件
        reviewClick(row) {
            const { reviewStatus, whetherRegulatory } = row;
            if (reviewStatus === 2) {
                this.unReview(row);
            } else if (
                whetherRegulatory === 1 &&
                this.formRight.supervisoryCodeAcquisition === "1"
            ) {
                this.whetherRegulatory(row); // 是否展示电子监管码弹窗
            } else {
                this.checkAndPosition(row); // 选中相同商品复核（相同编码）
            }
        },
        // 取消复核
        unReview(row) {
            const { allocationCode, productCode, reviewStatus, orderCode, mergeOrderCode } = row;
            console.log(row, 'row');

            let regulatoryCodeVOs = this.regulatoryCodeVOs[productCode];
            const params = {
                productCode: productCode, // 商品编码
                cancel: true,
                regulatoryCodeVOs: regulatoryCodeVOs,
                orderCode: orderCode,
                mergeOrderCode: mergeOrderCode,
                maxMediumPacking: row.maxMediumPacking,
            };
            if (reviewStatus === 2) {
                review(params).then((res) => {
                    const { code, msg, result } = res;
                    this.printStatus = true;
                    if (code === 0) {
                        this.regulatoryCodeVOs = [];
                        this.$message.success(msg);
                        this.initOrderTable(); // 刷新列表
                    } else {
                        this.$message.error(msg);
                        this.initOrderTable(); // 刷新列表
                    }
                });
            }
        },

        // 是否展示电子监管码弹窗
        whetherRegulatory(row) {
            const { allocationCode, productCode, whetherRegulatory, orderCode, mergeOrderCode } = row;
            // 需要扫描电子监管码的复核商品
            const goodsList = [];
            this.tableData.forEach((res) => {
                if (res.productCode === productCode) {
                    if (
                        res.exceptionCause !== null &&
                        res.exceptionReason !== undefined &&
                        res.exceptionNumber !== null
                    ) {
                        res.reviewStatus = 0;
                        return false;
                    }
                    goodsList.push(res);
                }
            });
            // 是否需要扫描电子监管码whetherRegulatory值0为否 ，1为是
            if (
                whetherRegulatory === 1 &&
                this.formRight.supervisoryCodeAcquisition === "1"
            ) {
                if (goodsList.length === 0) {
                    this.$message.warning("请获取需要扫描电子监管码的商品！");
                    return;
                }
                const obj = {
                    allocationCode: allocationCode,
                    productCode: productCode,
                    goods: goodsList,
                    orderCode: orderCode,
                    mergeOrderCode: mergeOrderCode,
                };
                this.$refs.tracingCodeScanDialog.open(obj);
                this.hasDialog = true;
            }
        },
        scanOrder() {
            this.abnormalTableData = []; // 复核前：清空异常数据
            this.apigetPartsInReviewGoodsSalesInfo();
            // this.initOrderTable()
        },
        // 面单号回车请求商品列表数据
        initOrderTable() {
            // 需要传入参数
            const params = {
                orderCode: this.formRight.orderCode, // 面单号
                allocationCode: this.formRight.allocationCode,
            };
            // 获取复核打包列表数据
            listPartsInReviewGoodsView(params).then((res) => {
                const { code, msg, result } = res;
                if (code !== 0) {
                    this.$message.error(msg);
                    this.$refs.trackingNumber.select();
                } else {
                    this.tableData = result;
                    this.tableData.forEach((item) => {
                        if (item.storageAttributes?.includes("液体")) {
                            item.liquid = "是"
                        } else {
                            item.liquid = "否"
                        }
                        if (!this.formRight.consolidationCode && this.boxTableData.length > 0) {
                            this.$refs.barCode.select();
                        } else {
                            this.$refs.boxCode.focus();
                        }
                    })
                    this.computeNumber(this.tableData);
                }
            });
        },
        // 异常数据对比
        abnormalData(resultList) {
            const abnormalData = this.abnormalTableData;
            if (!abnormalData.length) {
                return resultList;
            }
            resultList.forEach((item) => {
                abnormalData.forEach((obj) => {
                    // 判断id相同时异常确认并刷新列表
                    if (obj.id === item.id) {
                        item.exceptionNumber = obj.exceptionNumber;
                        item.exceptionCause = obj.exceptionCause;
                        item.exceptionReason = obj.exceptionReason;
                        item.remark = obj.remark; // 添加备注留言
                    }
                });
            });
            return resultList;
        },
        //耗材码回车事件
        boxCodeEnther() {
            // this.apisaveBoxCode();
            this.apiCheckboxCode()
        },
        // 商品条码回车事件
        barCodeEnter() {
            const trackingNumber = this.formRight.trackingNumber; // 面单号
            // 如果面单号为空时提示
            if (trackingNumber === "") {
                this.$message.warning("请先输入面单号！");
                return;
            }
            if (this.formRight.consolidationCode || this.boxTableData?.length == 0) {
                this.$message.error("请先输入耗材码后再进行操作");
                return;
            }
            const barCode = this.formRight.barCode; // 商品条码
            // 如果商品条码为空时提示
            if (barCode === "") {
                this.$message.warning("请输入商品条码！");
                return;
            }
            // 需要扫描电子监管码的复核商品
            const goodsList = [];
            // 循环判断商品列表
            const table_data = this.tableData;
            for (var i = 0; i < table_data.length; i++) {
                const obj = table_data[i];
                var barCodeArr = obj.barCode.split(",");
                for (var j = 0; j < barCodeArr.length; j++) {
                    if (barCode === barCodeArr[j]) {
                        if (obj.reviewStatus === 2) {
                            this.setReviewDetails(obj); // 设置展示商品主信息
                            this.$message.warning("该条形码对应的商品已复核！");
                            this.dottsFunction("商品已复核");
                            this.$refs.barCode.select(); // 商品条码获取焦点
                            return;
                        }
                        // 是否需要扫描电子监管码whetherRegulatory值0为否 ，1为是
                        if (obj.whetherRegulatory === 1) {
                            goodsList.push(obj);
                            const params = {
                                allocationCode: obj.allocationCode,
                                productCode: obj.productCode,
                                goods: goodsList,
                                orderCode: obj.orderCode,
                                mergeOrderCode: obj.mergeOrderCode,
                            };
                            this.$refs.tracingCodeScanDialog.open(params); // 是否展示电子监管码弹窗
                            this.hasDialog = true;
                        } else {
                            this.setReviewDetails(obj); // 设置展示商品主信息
                            this.$refs.barCode.select(); // 商品条码获取焦点
                            this.checkAndPosition(obj); // 选中相同商品复核（相同编码）
                        }
                        this.setReviewDetails(obj); // 设置展示商品主信息
                        return;
                    }
                }
            }
            this.$message.warning("找不到该条形码对应的商品！");
            // 清空商品中间已复核信息
            this.$refs.formData.resetFields();
            this.formData.packingUnit = "";
            this.formData.src = require("@/assets/images/product.png");
            this.$refs.barCode.select(); // 商品条码获取焦点
        },
        // 监管码扫描成功后
        changeTracing(row) {
            const tableList = this.tableData;
            tableList.forEach((item) => {
                // if (item.exceptionCause === null && item.exceptionReason === undefined && item.exceptionNumber === null) {
                //   if (item.productCode === row.productCode) {
                //     item.reviewStatus = 2
                //   }
                // } else {
                //   item.reviewStatus = 0
                // }
                if (item.productCode === row.productCode) {
                    this.regulatoryCodeVOs[row.productCode] = row.regulatoryCodeVOs;
                    if (
                        item.exceptionCause !== null &&
                        item.exceptionReason !== null &&
                        item.exceptionNumber !== null
                    ) {
                        item.reviewStatus = 0;
                        return false;
                    }
                    item.reviewStatus = 2;
                }
            });
            this.initOrderTable(); // 刷新列表
        },
        // 设置展示商品主信息
        setReviewDetails(obj) {
            const tableData = this.tableData;
            this.formData.src = obj.productPic?.[0];
            Object.assign(this.formData, obj);
            // 商品图片
            this.formData.src = obj.productPic?.[0];
            let realPickingNumber = 0;
            const batchNumberList = [];
            const produceTimeList = [];
            const validDateList = [];
            for (let i = 0; i < tableData.length; i++) {
                const d = tableData[i];
                if (d.productCode === obj.productCode) {
                    this.inNum++;
                }
            }

            const batchNumberMap = {};
            const produceTimeMap = {};
            const validDateMap = {};

            // 计算批号数量
            for (let i = 0; i < tableData.length; i++) {
                const d = tableData[i];
                if (d.productCode === obj.productCode) {
                    const batchNumber = d.batchNumber;
                    if (batchNumberMap[batchNumber]) {
                        batchNumberMap[batchNumber] =
                            parseInt(batchNumberMap[batchNumber]) + parseInt(d.reviewNumber);
                    } else {
                        batchNumberMap[batchNumber] = d.reviewNumber;
                        produceTimeMap[batchNumber] = d.produceTime;
                        validDateMap[batchNumber] = d.validDate;
                    }
                    realPickingNumber += parseInt(d.reviewNumber);
                }
            }

            let ll = 0;
            let batchNumberMapLength = 0;
            for (const key in batchNumberMap) {
                // eslint-disable-line no-unused-vars
                batchNumberMapLength++;
            }

            for (const key in batchNumberMap) {
                const batchNumber = key;
                const reviewNumber = batchNumberMap[batchNumber];
                const produceTime = produceTimeMap[batchNumber];
                const validDate = validDateMap[batchNumber];

                if (ll === batchNumberMapLength - 1) {
                    batchNumberList.push(batchNumber + "(" + reviewNumber + ")");
                    produceTimeList.push(produceTime);
                    validDateList.push(validDate);
                } else {
                    batchNumberList.push(batchNumber + "(" + reviewNumber + ")");
                    produceTimeList.push(produceTime);
                    validDateList.push(validDate);
                }
                ll++;
            }
            // 批号转化
            const batchNum = batchNumberList.toString().replace('"', "");
            // 商品批号
            this.formData.batchNumber = batchNum;
            // 日期转化
            const produceTime1 = produceTimeList.toString();
            // 循环多个生产日期
            if (produceTimeList.length > 1) {
                this.formData.produceTime = produceTimeList
                    .map((item) => {
                        return XEUtils.toDateString(item, "yyyy-MM-dd");
                    })
                    .join(",")
                    .replace("[]", "");
            } else {
                // 生产日期日期转换
                this.formData.produceTime = XEUtils.toDateString(
                    produceTime1,
                    "yyyy-MM-dd"
                );
            }
            const validDate1 = validDateList.toString();
            // 循环多个生产日期
            if (validDateList.length > 1) {
                this.formData.validDate = validDateList
                    .map((item) => {
                        return XEUtils.toDateString(item, "yyyy-MM-dd");
                    })
                    .join(",")
                    .replace("[]", "");
            } else {
                // 有效期至日期转换
                this.formData.validDate = XEUtils.toDateString(
                    validDate1,
                    "yyyy-MM-dd"
                );
            }
            // console.log(validDate1,this.formData.validDate, "this.formData.validDate");
            
            // 复核数量
            this.formData.reviewNumber = realPickingNumber;
        },
        // 双击选中行并定位 \ 选中相同商品(同编码)
        checkAndPosition(row) {
            // 商品编码的隐藏值
            const { allocationCode, productCode, orderCode, mergeOrderCode } = row;
            console.log(row, "row");

            if (productCode === "") {
                return;
            }
            // 获取所有商品行
            const tableRow = this.tableData;
            const ids = [];
            let num = 0;
            tableRow.forEach((i, v) => {
                if (productCode === i.productCode) {
                    /**
                     * setCheckboxRow(rows, checked)用于 type=checkbox，设置行为选中状态，
                     * 第二个参数为选中与否rows: Row | Array<Row>, checked: boolean
                     * */
                    // 设置选中行
                    this.$refs.reviewTable.setCheckboxRow(i, true);
                    const id = i.id;
                    num += i.reviewNumber;
                    ids.push(id);
                }
            });
            const params = {
                productCode: productCode, // 商品编码
                orderCode: orderCode,
                mergeOrderCode: mergeOrderCode,
                maxMediumPacking: row.maxMediumPacking
            };
            // 商品复核请求
            review(params).then((res) => {
                const { code, msg, result } = res;
                if (code === 0) {
                    this.$message.success(msg);
                    this.initOrderTable(); // 刷新列表
                    this.dottsFunction(num + row.packingUnit);
                } else {
                    this.$message.error(msg);
                }
            });
            // 如果id长度超过表格，设置滚动条
            if (ids.length) {
                this.scrollTop(row, productCode);
            }
        },
        /**
         *  设置滚动条位置:如果有滚动条，则滚动到对应的行（对于某些特定的场景可能会用到，比如定位到某一行）
         *   row: Row, column?: ColumnConfig
         * */
        scrollTop(row, column) {
            this.$refs.reviewTable.scrollToRow(row, column);
        },
        // 查看任务
        searchBtn() {
            this.$refs.viewTasksDialog.open();
            this.hasDialog = true;
        },
        // 复核确认
        reviewBtn() {
            if (this.formRight.consolidationCode || this.boxTableData?.length === 0) {
                this.$message.error("请先输入耗材码后再进行操作");
                return;
            }
            const tableList = this.tableData?.length > 0 ? this.tableData : [];
            if (tableList?.length === 0) {
                this.$message.warning("当前没有商品可以复核！");
                return;
            }
            const params = {
                // exceptionTasks: this.abnormalTableData,
                boxCode: this.boxTableData[0].boxCode,
                orderCode: this.formRight.orderCode,
            };
            // 复核方法
            reviewConfirm(params).then((res) => {
                const { code, msg, result } = res;
                if (code !== 0) {
                    this.$message.error(msg);
                } else {
                    this.$message.success(msg || "复核成功");
                    this.$refs.trackingNumber.focus();
                    this.clearPage();
                }
            });
            this.hasDialog = false;
        },

        // 计算商品已检数量、品种已检数量
        computeNumber(rowList) {
            const reviewNumber = []; // 已复核的复核数量
            const productCodeNumber = []; // 商品编码数量
            const productNumber = this.formRight.productNumber; // 商品已检数量
            const inreviewProductNumber = this.formRight.inreviewProductNumber; // 品种已检数量
            let filterArr = rowList.filter((item) => {
                const review = item.reviewStatus;
                return review === 2;
            });
            this.formRight.productNumber = filterArr.reduce((total, item) => {
                return total + item.reviewNumber;
            }, 0);
            this.formRight.inreviewProductNumber = filterArr.length;
        },
        // 判断打印值
        returnBoole(val) {
            return val ? 1 : 0;
        },

        // esc关闭弹窗后继续操作，快捷键不可操作
        closeDialog() {
            this.hasDialog = false;
        },
        // 查看任务弹窗关闭后面单号获取焦点
        closeViewTasksDialog() {
            this.hasDialog = false;
            this.$refs.trackingNumber.focus();
        },
        // 行颜色
        rowClassName({ row, rowIndex }) {
            // reviewStatus复核状态 0 索取 1 1次复核完成 2 二次复核完成
            // const { reviewStatus, exceptionCause, exceptionReason, exceptionNumber } =
            //     row;
            // if (reviewStatus === 2) {
            //     return "row-green"; // Lightgreen浅绿色
            // } else {
            //     if (
            //         exceptionCause !== null &&
            //         exceptionReason !== null &&
            //         exceptionNumber !== null
            //     ) {
            //         return "row-red"; // 异常提交后展示红色
            //     }
            // }
            let rowName = "";
                if (row.reviewStatus == 2) {
                    rowName = "cell-green";
                } else if (row.exceptionStatus !== null) {
                    rowName = "cell-red";
                } else {
                    rowName = "cell-white";
                }
                if (this.checkRow) {
                    rowName = this.checkRow.barCode === row.barCode ? "cell-blue" : rowName;
                }
            return rowName;
        },
        //---------------------------api-----------------------------
        //复合打包，索取
        apigetPartsInReviewGoodsSalesInfo() {
            const trackingNumber = this.formRight.trackingNumber; // 面单号
            // 如果面单号为空
            if (trackingNumber === "") {
                this.$message.warning("请先输入面单号！");
                return;
            }

            // 需要传入参数
            const params = {
                trackingNumber: trackingNumber, // 面单号
            };
            getPartsInReviewGoodsSalesInfo(params).then((res) => {
                const { code, msg, result } = res;
                if (code !== 0) {
                    this.clearPage();
                    this.$message.error(msg);
                } else {
                    this.formRight = {
                        orderCode: result.orderCode, //出库单号
                        consolidationCode: result.consolidationCode, //拼箱号，如果有拼箱号，需要扫描耗材码
                        allocationCode: result.allocationCode, // 分配单号
                        boxCode: "", // 小车号
                        trackingNumber: this.formRight.trackingNumber, // 面单号
                        barCode: "", // 商品条码
                        productNumberAll: result.productNumberAll, // 商品总数量
                        productNumber: result.productNumber, // 商品已检数量
                        inreviewProductNumberAll: result.inreviewProductNumberAll, // 品种数总数量
                        inreviewProductNumber: result.inreviewProductNumber, // 品种已检数
                        supervisoryCodeAcquisition: result.supervisoryCodeAcquisition, // 电子监管码采集
                    };
                    if (!this.formRight.consolidationCode && this.boxTableData.length > 0) {
                        this.$refs.barCode.select();
                    } else {
                        this.$refs.boxCode.focus();
                    }

                    this.apivideoStart(result.mergeOrderCode, result.isVideoEnabled);
                    this.initOrderTable();
                }
            });
        },
        //开启摄像头
        apivideoStart(orderCode, isVideoEnabled) {
            const options = {
                method: "POST",
                url: "http://127.0.0.1:9095/video/start?bizCode=" + orderCode,
            };
            axios(options)
                .then((res) => {
                    const { code, msg, result } = res.data;
                    if (code === 0) {
                        this.videoId = result;
                        clearInterval(this.intervalId);
                        this.apivideoPing();
                    } else {
                        this.$message.error(msg || "摄像头开启失败");
                        if (isVideoEnabled === "1") {
                            setTimeout(() => {
                                this.apivideoStart(orderCode, isVideoEnabled);
                            }, 1000);
                        }
                    }
                })
                .catch((err) => {
                    if (isVideoEnabled !== "1") {
                        return;
                    }
                    this.$alert("请打开摄像头后点击重试?", "提示", {
                        confirmButtonText: "重试",
                        type: "warning",
                        callback: () => {
                            this.apivideoStart(mergeOrderCode, isVideoEnabled);
                        },
                    });
                });
        },

        //维持摄像头心跳
        apivideoPing() {
            this.intervalId = setInterval(() => {
                const options = {
                    method: "POST",
                    url: "http://127.0.0.1:9095/video/ping?videoId=" + this.videoId,
                };
                axios(options).then((res) => {
                    const { code, msg, result } = res.data;
                    if (code !== 0) {
                        this.$message.error(msg || "无法连接视频监控客户端");
                    }
                });
            }, 5000);
        },
        apisaveBoxCode() {
            saveBoxCode({
                orderCode: this.formRight.orderCode,
                boxCode: this.formRight.boxCode,
            }).then((res) => {
                const { code, msg, result } = res;
                if (code === 0) {
                    this.$message.success("保存成功");
                    this.formRight.consolidationCode = undefined;
                    this.$refs.barCode.select();
                    this.initOrderTable();
                } else {
                    this.formRight.boxCode = "";
                    this.$refs.boxCode.focus();
                    this.$message.error(msg);
                }
            });
        },
        apiCheckboxCode() {
            checkParts({
                boxCode: this.formRight.boxCode,
            }).then(res => {
                const { code, msg, result } = res;
                if (code === 0) {
                    if (this.boxTableData?.length > 0) {
                        this.boxTableData.splice(0, 1, result);
                    } else {
                        this.boxTableData.push(result);
                    }
                    this.$message.success(msg);
                    this.formRight.consolidationCode = undefined;
                    this.$refs.barCode.select();
                    this.initOrderTable();
                } else {
                    this.formRight.boxCode = "";
                    this.$refs.boxCode.focus();
                    this.$message.error(msg);
                }
            })
        },
        //删除拼箱
        deleteBox(row) {
            // const params = {
            //   mergeOrderCode: this.mergeOrderCode,
            //   consolidationCode: row.consolidationCode
            // }
            // deleteMergeOrder(params).then(res => {
            //   const {code, msg, result} = res
            //   if(code === 0){
            //     this.$message.success(msg)
            //     // this.apigetPartsInReviewGoodsView()
            //   }else{
            //     this.$message.error(msg)
            //   }
            // })
            this.boxTableData.splice(this.boxTableData.indexOf(row), 1)
            // this.formRight.consolidationNumber = this.boxTableData.length
        },
        clearPage() {
            this.formRight = {
                orderCode: "", //出库单号
                consolidationCode: "", //拼箱号，如果有拼箱号，需要扫描耗材码
                allocationCode: "", // 分配单号
                boxCode: "", // 小车号
                trackingNumber: "", // 面单号
                barCode: "", // 商品条码
                productNumberAll: "", // 商品总数量
                productNumber: "", // 商品已检数量
                inreviewProductNumberAll: "", // 品种数总数量
                inreviewProductNumber: "", // 品种已检数
                supervisoryCodeAcquisition: "", // 电子监管码采集
            };
            this.formData = {
                productName: "", // 商品名称
                specifications: "", // 规格
                batchNumber: "", // 批号
                produceTime: "", // 生产日期
                validDate: "", // 有效期至
                reviewNumber: "", // 复核数量
                packingUnit: "", // 单位
                specialAttributes: "", // 特殊属性
                manufacturer: "", // 生产厂家
                packagingRequirement: "", // 包装要求
                mediumPacking: "", // 中包装数量
                src: require("@/assets/images/product.png"),
            };
            this.tableData = [];
            this.boxTableData = [];
        },
        //----------------------------tool-----------------------
        //语音播报
        dottsFunction(ttsMsg) {
            doTTS(ttsMsg);
        },
    },
};
</script>

<style lang="scss" scoped>
.el-card {
    padding: 6px 15px;

    .clearfix[data-v-8d34e40c] {
        display: inline-block;
        float: none;
    }

    .header-right {
        line-height: 30px;
        font-size: 14px;
        float: right;

        label {
            display: inline-block;
            width: 60px;
        }
    }
}

.el-main {
    border: 1px solid #e8eaec;
    background-color: #fff;
}

.form-right {
    background-color: #fff;
    padding: 20px;
    float: right;
    border: 1px solid #e8eaec;

    .el-form-item {
        .el-input {
            width: 270px !important;
        }
    }
}

.large-size {
    font-size: 36px;
    color: blue;
    font-weight: bold;
    display: inline-block;
    width: 740px;
    white-space: nowrap;
    /*强制span不换行*/
    overflow: hidden;
    /*超出宽度部分隐藏*/
}

.medium-size {
    font-weight: bold;
    font-size: 28px;
    line-height: 28px;
}

.el-form-item,
.el-form-item--small.el-form-item {
    height: 45px;
}

.el-form-item__label {
    font-weight: unset;
    font-size: 16px;
    width: 92px;
}

.el-image {
    width: 300px;
    height: 300px;
}

.head-title {
    text-align: center;
    border-top: 1px solid #e8eaec;
    border-bottom: 1px solid #e8eaec;
    height: 60px;
    line-height: 60px;
    font-size: 20px;
}

.item-label {
    line-height: 60px;
    font-weight: unset;
    font-size: 26px;
}

.item-number {
    font-size: 50px;
}
::v-deep .cell-white {
  background-color: white !important;
}

::v-deep .cell-green {
  background-color: lightgreen !important;
}

::v-deepe .cell-red {
  background-color: red !important;
}

::v-deep .cell-blue {
  background-color: lightblue !important;
}
</style>