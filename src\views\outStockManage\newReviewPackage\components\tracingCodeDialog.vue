<template>
    <el-dialog :visible.sync="dialogVisible" width="800px" :close-on-click-modal="false" :close-on-press-escape="true"
        :show-close="false" :modal-append-to-body="true" :append-to-body="true" @close="handleClose"
        @keydown.esc.native="handleClose">

        <!-- 自定义标题栏 -->
        <div slot="title" class="dialog-title">
            <span class="title-text">追溯码扫码</span>
            <div class="title-buttons">
                <el-button size="small" @click="saveAndClose" :loading="saveCloseLoading">保存并关闭</el-button>
                <el-button type="danger" size="small" @click="handleNoTracingCode" :disabled="loading">
                    实物无追溯码
                </el-button>
                <el-button size="small" @click="handleClose">
                    关闭(ESC)
                </el-button>
            </div>
        </div>

        <!-- 扫码数量和包装信息 -->
        <div class="scan-info-section">
            <div class="scan-count">
                <span class="label">扫码数量</span>
                <span class="count">{{ scannedCount }}</span>
            </div>
            <div class="package-info">
                <div class="package-row">
                    <span>件包装：<span class="package-value">{{ fixedInformation.packingNumber || 0 }}</span></span>
                    <span>中包装：<span class="package-value">{{ fixedInformation.mediumNumber || 0 }}</span></span>
                </div>
                <div class="package-row">
                    <span v-if="packageCodeStatus.piecePackageCode === 1">件包装：<span
                            class="status has-code">有码</span></span>
                    <span v-else>件包装：<span class="status no-code">无码</span></span>
                    <span v-if="packageCodeStatus.mediumPackageCode === 1">中包装：<span
                            class="status has-code">有码</span></span>
                    <span v-else>中包装：<span class="status no-code">无码</span></span>
                    <span v-if="packageCodeStatus.smallPackageCode === 1">小包装：<span
                            class="status has-code">有码</span></span>
                    <span v-else>小包装：<span class="status no-code">无码</span></span>
                </div>
            </div>
        </div>

        <!-- 商品信息 -->
        <div class="product-info-section">
            <div class="product-row">
                <span class="label">商品名称</span>
                <span class="value">{{ fixedInformation.productName }} {{ fixedInformation.specifications }}</span>
            </div>
            <div class="product-row">
                <span class="label">商品批号</span>
                <div class="batch-info">
                    <div v-for="(batch, index) in batchList" :key="index" class="batch-item"
                        :class="{ 'active-batch': batch.isActive }">
                        <span class="batch-number">{{ batch.batchNumber }}({{ batch.quantity }})</span>
                    </div>
                    <el-button type="text" size="small" @click="handleClearTracingCodes" :disabled="loading">
                        清空追溯码
                    </el-button>
                </div>
            </div>
        </div>

        <!-- 扫码推荐区域 - 三列布局 -->
        <div class="scan-recommend-section">
            <!-- 左侧区域 - 扫码推荐数据 -->
            <div class="recommend-data-area">
                <div class="recommend-title">扫码推荐：</div>
                <div class="recommend-list">
                    <div class="recommend-item" v-if="packageCodeStatus.mediumPackageCode === 1">
                        <span class="recommend-label">中包装</span>
                        <span class="recommend-value">x{{ recommendInfo.mediumCount || 0 }}</span>
                    </div>
                    <div class="recommend-item" v-if="packageCodeStatus.smallPackageCode === 1">
                        <span class="recommend-label">小包装</span>
                        <span class="recommend-value">x{{ recommendInfo.smallCount || 0 }}</span>
                    </div>
                    <div class="recommend-item total-scan" v-if="recommendInfo.totalScanCount > 0">
                        <span class="recommend-label">总扫码数</span>
                        <span class="recommend-value">{{ recommendInfo.totalScanCount }}</span>
                    </div>
                </div>
            </div>

            <!-- 中间区域 - 扫码操作区 -->
            <div class="scan-operation-area">
                <div class="package-selection">
                    <el-radio-group v-model="formData.codeLevel" @change="codeLevelChange">
                        <!-- <el-radio label="3" v-if="packageCodeStatus.piecePackageCode === 1">件包装</el-radio> -->
                        <el-radio label="2"
                            v-if="packageCodeStatus.mediumPackageCode === 1 && recommendInfo.mediumCount > 0">中包装</el-radio>
                        <el-radio label="1" v-if="packageCodeStatus.smallPackageCode === 1">小包装</el-radio>
                    </el-radio-group>
                </div>
                <div class="scan-input">
                    <el-input ref="tracingCodeInput" v-model="formData.regulatoryCode" placeholder="请扫描追溯码"
                        @keydown.enter.native="handleScanCode" :disabled="loading" />
                </div>
            </div>

            <!-- 右侧区域 - 扫码统计 -->
            <div class="scan-statistics-area">
                <div class="statistics-label">扫码进度</div>
                <div class="statistics-value">{{ currentScanCount }}</div>
            </div>
        </div>

        <!-- 追溯码列表 -->
        <div class="tracing-code-list">
            <el-table :data="tracingCodeList" :height="tableHeight" size="small" border>
                <el-table-column prop="regulatoryCode" label="电子监管码" min-width="200" />
                <el-table-column label="包装类型" min-width="100">
                    <template slot-scope="scope">
                        {{ scope.row.codeLevel === '1' ? '小包装' : scope.row.codeLevel === '2' ? '中包装' : '件包装' }}
                    </template>
                </el-table-column>
                <el-table-column prop="pkgAmount" label="完成数量" min-width="100" />
                <!-- <el-table-column label="来源" min-width="80">
                    <template slot-scope="scope">
                        <el-tag v-if="scope.row.isAutoFilled" type="success" size="mini">自动填充</el-tag>
                        <el-tag v-else type="info" size="mini">手动扫码</el-tag>
                    </template>
                </el-table-column> -->
                <el-table-column label="操作" min-width="80">
                    <template slot-scope="scope">
                        <el-button type="text" size="small" @click="handleDeleteCode(scope.$index)" :disabled="loading">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 底部按钮 -->
        <!-- <div slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleConfirm" :loading="loading" :disabled="!canConfirm">
                确定
            </el-button>
        </div> -->

        <!-- 解锁弹窗 -->
        <unlock-dialog ref="unlockDialog" @on-close="handleUnlockConfirm" />
    </el-dialog>
</template>

<script>
import UnlockDialog from './unlockDialog.vue';
import { review, getTraceCode, saveTraceCode } from '@/api/outstock/fhdb';
import {
    unlockThisTask,
} from '@/api/outstock/index.js'

export default {
    name: 'TracingCodeDialog',
    components: {
        UnlockDialog
    },
    data() {
        return {
            dialogVisible: false,
            loading: false,

            // 默认信息（从父组件传入）
            defaultInformation: {},

            // 商品数据列表（按批号分组）
            tableData: [],

            // 当前处理的批号索引
            readIndex: 0,

            // 固定信息（当前批号的商品信息）
            fixedInformation: {
                receiptNumber: '',      // 单据编号
                ownerName: '',          // 业主名称
                clientName: '',         // 客户名称
                productName: '',        // 商品名称
                specifications: '',     // 商品规格
                mediumNumber: 0,        // 中包装数量
                packingNumber: 0,       // 件包装数量
            },

            // 包装追溯码状态（当前批号的包装追溯码状态）
            packageCodeStatus: {
                smallPackageCode: 0,    // 小包装追溯码状态
                mediumPackageCode: 0,   // 中包装追溯码状态
                piecePackageCode: 0,    // 件包装追溯码状态
            },

            // 推荐扫码数据（当前批号的推荐扫码次数）
            recommendScanData: {
                mediumPackageCount: 0,  // 中包装推荐扫码次数
                smallPackageCount: 0,   // 小包装推荐扫码次数
                totalScanCount: 0,      // 总扫码数
            },

            // 可变信息（当前批号的扫码进度）
            variableInformation: {
                number: 0,              // 当前批号总数量
                needScanNumber: 0,      // 需扫描数量
                needScanTime: 0,        // 需扫描次数
                scanNumber: 0,          // 已扫描数量
                scanTime: 0,            // 已扫描次数
                big: 0,                 // 已扫描件包装数
                medium: 0,              // 已扫描中包装数
                small: 0,               // 已扫描小包装数
            },

            // 包装级别选项
            codeLevels: [
                { code: "1", name: "小包装" },
                { code: "2", name: "中包装" },
                { code: "3", name: "件包装" },
            ],

            // 扫码表单
            formData: {
                regulatoryCode: '',     // 追溯码
                codeLevel: '1',         // 包装规格
                pkgAmount: 1,           // 包装数量
            },

            // 扫码相关
            tracingCodeInput: '',
            tracingCodeList: [],

            // 提交数据
            submitData: [],
            saveCloseLoading: false, // 保存并关闭按钮加载状态
        };
    },
    computed: {
        // 扫码数量（当前商品的复核数量，固定值）
        scannedCount() {
            return this.variableInformation.number || 0;
        },

        // 已扫描数量
        actualScannedCount() {
            return this.variableInformation.scanNumber || 0;
        },

        // 当前扫描数量显示
        currentScanCount() {
            const total = this.variableInformation.number || 0;
            return `${this.actualScannedCount}/${total}`;
        },

        // 是否可以确认（所有批号都扫码完成）
        canConfirm() {
            return this.readIndex >= this.tableData.length - 1 &&
                this.variableInformation.needScanNumber <= 0;
        },

        // 当前批号信息
        currentBatch() {
            return this.tableData[this.readIndex] || {};
        },

        // 批号列表显示
        batchList() {
            return this.tableData.map((item, index) => ({
                batchNumber: item.batchNumber || '',
                quantity: item.reviewNumber || 0,
                isActive: index === this.readIndex
            }));
        },

        // 扫码推荐信息
        recommendInfo() {
            // 优先使用接口返回的推荐扫码数据
            if (this.recommendScanData.totalScanCount > 0) {
                return {
                    mediumCount: this.recommendScanData.mediumPackageCount || 0,
                    smallCount: this.recommendScanData.smallPackageCount || 0,
                    totalScanCount: this.recommendScanData.totalScanCount || 0
                };
            }

            // 兜底逻辑：根据包装数量计算推荐扫码次数
            const mediumPacking = this.fixedInformation.mediumNumber || 1;
            const needScanNumber = this.variableInformation.needScanNumber || 0;

            return {
                mediumCount: Math.ceil(needScanNumber / mediumPacking),
                smallCount: needScanNumber,
                totalScanCount: needScanNumber
            };
        },

        // 可用的包装类型选项（根据追溯码状态动态生成）
        availableCodeLevels() {
            const levels = [];
            if (this.packageCodeStatus.piecePackageCode === 1) {
                levels.push({ code: "3", name: "件包装" });
            }
            if (this.packageCodeStatus.mediumPackageCode === 1) {
                levels.push({ code: "2", name: "中包装" });
            }
            if (this.packageCodeStatus.smallPackageCode === 1) {
                levels.push({ code: "1", name: "小包装" });
            }
            return levels;
        },

        // 动态计算表格高度，避免双重滚动条
        tableHeight() {
            // 根据屏幕高度动态计算合适的表格高度
            const screenHeight = window.innerHeight || document.documentElement.clientHeight;

            if (screenHeight <= 600) {
                return 230; // 小屏幕
            } else if (screenHeight <= 768) {
                return 250; // 中等屏幕
            } else {
                return 300; // 大屏幕
            }
        }
    },
    methods: {
        // 打开弹窗
        open(defaultInformation) {
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs.tracingCodeInput?.focus();
            });

            this.defaultInformation = defaultInformation || {};

            // 重置数据
            this.readIndex = 0;
            this.submitData = [];
            this.dataProcessing();

            // 查询临时存储的追溯码并自动填充（使用 nextTick 确保 dataProcessing 完成后执行）
            this.$nextTick(() => {
                this.loadTemporaryTraceCodes();
            });
        },

        // 查询临时存储的追溯码并自动填充
        async loadTemporaryTraceCodes() {
            try {
                // console.log('开始查询临时追溯码...');
                // console.log('defaultInformation:', this.defaultInformation);
                // console.log('tableData:', this.tableData);
                // console.log('readIndex:', this.readIndex);

                // 获取合单号和当前商品编码
                const mergeOrderCode = this.defaultInformation.mergeOrderCode;
                const currentProduct = this.tableData[this.readIndex];

                // console.log('mergeOrderCode:', mergeOrderCode);
                // console.log('currentProduct:', currentProduct);

                if (!mergeOrderCode || !currentProduct?.productCode) {
                    console.log('缺少必要参数，跳过临时追溯码查询');
                    console.log('mergeOrderCode存在:', !!mergeOrderCode);
                    console.log('currentProduct存在:', !!currentProduct);
                    console.log('productCode存在:', !!currentProduct?.productCode);
                    return;
                }

                // console.log('查询临时追溯码:', { mergeOrderCode, productCode: currentProduct.productCode });

                // 调用接口查询临时存储的追溯码
                const response = await getTraceCode({
                    mergeOrderCode: mergeOrderCode,
                    productCode: currentProduct.productCode
                });

                // console.log('接口响应:', response);

                if (response.code === 0 && response.result && response.result.length > 0) {
                    // console.log('获取到临时追溯码:', response.result);

                    // 自动填充追溯码
                    this.autoFillTraceCodes(response.result);
                } else {
                    console.log('未找到临时追溯码数据，响应:', response);
                }
            } catch (error) {
                console.error('查询临时追溯码失败:', error);
                // 不显示错误提示，静默处理
            }
        },

        // 自动填充追溯码
        autoFillTraceCodes(traceCodes) {
            if (!traceCodes || traceCodes.length === 0) {
                console.log('追溯码数据为空，跳过填充');
                return;
            }

            // console.log('开始自动填充追溯码:', traceCodes);
            // console.log('当前批号索引:', this.readIndex);
            // console.log('当前批号信息:', this.currentBatch);
            // console.log('填充前的tracingCodeList:', this.tracingCodeList);

            // 为当前批号和后续批号填充追溯码
            this.fillTraceCodesForBatches(traceCodes);

            // console.log('填充后的tracingCodeList:', this.tracingCodeList);
        },

        // 为多个批号填充追溯码
        fillTraceCodesForBatches(traceCodes) {
            let remainingCodes = [...traceCodes];

            // console.log('开始为多个批号填充追溯码:', {
            //     totalCodes: remainingCodes.length,
            //     currentReadIndex: this.readIndex,
            //     totalBatches: this.tableData.length
            // });

            // 使用递归方式处理，以便在批号跳转后能够继续处理剩余追溯码
            this.fillRemainingCodes(remainingCodes);
        },

        // 递归填充剩余追溯码
        fillRemainingCodes(remainingCodes) {
            if (remainingCodes.length === 0) {
                console.log('所有追溯码已填充完成');
                return;
            }

            if (this.readIndex >= this.tableData.length) {
                console.log(`还有 ${remainingCodes.length} 个追溯码未填充，但已无更多批号`);
                return;
            }

            const currentBatch = this.tableData[this.readIndex];
            if (!currentBatch) {
                console.log('当前批号数据不存在');
                return;
            }

            // 计算当前批号需要的追溯码数量
            const needScanNumber = this.calculateNeedScanNumber(currentBatch);
            // console.log(`当前批号 ${currentBatch.batchNumber} 需要 ${needScanNumber} 个追溯码`);

            if (needScanNumber > 0) {
                // 从剩余追溯码中取出需要的数量
                const codesToFill = remainingCodes.splice(0, Math.min(needScanNumber, remainingCodes.length));

                if (codesToFill.length > 0) {
                    // console.log(`为当前批号 ${currentBatch.batchNumber} 填充 ${codesToFill.length} 个追溯码`);

                    // 填充到当前批号（不立即检查完成状态）
                    this.fillCurrentBatchTraceCodes(codesToFill, false);

                    // 手动检查批号完成状态
                    const hasNextBatch = this.checkBatchCompleteForAutoFill();

                    // 如果当前批号已完成且跳转到了下一批号，继续处理剩余追溯码
                    if (hasNextBatch && remainingCodes.length > 0) {
                        // console.log('批号已跳转，继续处理剩余追溯码');
                        // 使用 setTimeout 确保 dataProcessing 完成后再继续
                        setTimeout(() => {
                            this.fillRemainingCodes(remainingCodes);
                        }, 100);
                    } else if (remainingCodes.length > 0) {
                        console.log(`还有 ${remainingCodes.length} 个追溯码未填充，但当前批号未完成或无更多批号`);
                    }
                } else {
                    console.log('没有追溯码可填充');
                }
            } else {
                // console.log('当前批号不需要追溯码，跳转到下一批号');
                // 当前批号不需要追溯码，直接跳转到下一批号
                this.readIndex++;
                if (this.readIndex < this.tableData.length) {
                    this.dataProcessing();
                    setTimeout(() => {
                        this.fillRemainingCodes(remainingCodes);
                    }, 100);
                } else {
                    // console.log(`还有 ${remainingCodes.length} 个追溯码未填充，但已无更多批号`);
                }
            }
        },

        // 计算批号需要扫描的数量
        calculateNeedScanNumber(batchData) {
            // 根据包装追溯码状态计算需要扫描的次数
            // 注意：包装追溯码状态字段直接在 batchData 上，不是在 packageCodeStatus 子对象中
            const packageCodeStatus = {
                smallPackageCode: batchData.smallPackageCode || 0,
                mediumPackageCode: batchData.mediumPackageCode || 0,
                piecePackageCode: batchData.piecePackageCode || 0
            };
            const mediumNumber = batchData.maxMediumPacking || 0;
            const packingNumber = batchData.maxLargePacking || 0;

            let needScanCount = 0;

            // console.log('计算需要扫描数量:', {
            //     batchData,
            //     packageCodeStatus,
            //     mediumNumber,
            //     packingNumber
            // });

            // 小包装有码：扫描商品数量次
            if (packageCodeStatus.smallPackageCode === 1) {
                needScanCount = batchData.reviewNumber || batchData.number || 0;
                // console.log('使用小包装扫码，needScanCount:', needScanCount);
            }
            // 中包装有码：扫描中包装数量次
            else if (packageCodeStatus.mediumPackageCode === 1 && mediumNumber > 0) {
                needScanCount = Math.ceil((batchData.reviewNumber || batchData.number || 0) / mediumNumber);
                // console.log('使用中包装扫码，needScanCount:', needScanCount);
            }
            // 件包装有码：扫描件包装数量次
            else if (packageCodeStatus.piecePackageCode === 1 && packingNumber > 0) {
                needScanCount = Math.ceil((batchData.reviewNumber || batchData.number || 0) / packingNumber);
                // console.log('使用件包装扫码，needScanCount:', needScanCount);
            }
            // 兜底逻辑：如果所有包装类型都没有追溯码，但是有缓存的追溯码数据，则默认使用小包装
            else {
                // console.log('所有包装类型都没有追溯码，使用兜底逻辑');
                needScanCount = batchData.reviewNumber || batchData.number || 0;
            }

            // console.log('计算结果 needScanCount:', needScanCount);
            return needScanCount;
        },

        // 为当前批号填充追溯码
        fillCurrentBatchTraceCodes(traceCodes, checkComplete = true) {
            traceCodes.forEach(traceCodeData => {
                let traceCodeItem;

                // 判断数据格式：如果是字符串，则按原逻辑处理；如果是对象，则直接使用对象数据
                if (typeof traceCodeData === 'string') {
                    // 字符串格式（预填充追溯码）
                    const codeLevel = this.determineCodeLevel();
                    const pkgAmount = this.determinePkgAmount(codeLevel);

                    traceCodeItem = {
                        regulatoryCode: traceCodeData,
                        codeLevel: codeLevel,
                        pkgAmount: pkgAmount,
                        batchNumber: this.currentBatch.batchNumber,
                        isAutoFilled: true
                    };
                } else {
                    // 对象格式（接口返回的缓存数据）
                    traceCodeItem = {
                        regulatoryCode: traceCodeData.regulatoryCode,
                        codeLevel: traceCodeData.codeLevel,
                        pkgAmount: traceCodeData.pkgAmount,
                        batchNumber: this.currentBatch.batchNumber,
                        isAutoFilled: true
                    };
                }

                // console.log('添加追溯码到列表:', traceCodeItem);
                this.tracingCodeList.push(traceCodeItem);

                // 更新扫描统计
                this.updateScanStatistics(traceCodeItem);
            });

            // console.log(`当前批号填充完成，已填充 ${traceCodes.length} 个追溯码`);
            // console.log('当前追溯码列表:', this.tracingCodeList);
            // console.log('当前扫描进度:', {
            //     needScanNumber: this.variableInformation.needScanNumber,
            //     needScanTime: this.variableInformation.needScanTime,
            //     scanNumber: this.variableInformation.scanNumber
            // });

            // 检查当前批号是否完成（仅在自动填充时检查）
            if (checkComplete) {
                this.checkBatchCompleteForAutoFill();
            }
        },

        // 存储批号追溯码（用于后续批号）
        storeBatchTraceCodes(batchIndex, traceCodes) {
            if (!this.tableData[batchIndex]) {
                return;
            }

            // 在批号数据中存储预填充的追溯码
            if (!this.tableData[batchIndex].preFilledTraceCodes) {
                this.tableData[batchIndex].preFilledTraceCodes = [];
            }

            this.tableData[batchIndex].preFilledTraceCodes.push(...traceCodes);
            // console.log(`为批号 ${this.tableData[batchIndex].batchNumber} 存储了 ${traceCodes.length} 个预填充追溯码`);
        },

        // 确定追溯码的包装级别
        determineCodeLevel() {
            // 根据当前批号的包装追溯码状态确定级别
            const packageCodeStatus = this.packageCodeStatus;

            // 优先选择有码的包装级别
            if (packageCodeStatus.smallPackageCode === 1) {
                return "1"; // 小包装
            } else if (packageCodeStatus.mediumPackageCode === 1) {
                return "2"; // 中包装
            } else if (packageCodeStatus.piecePackageCode === 1) {
                return "3"; // 件包装
            }

            // 默认小包装
            return "1";
        },

        // 确定包装数量
        determinePkgAmount(codeLevel) {
            if (codeLevel === "1") {
                return 1; // 小包装默认1
            } else if (codeLevel === "2") {
                return this.fixedInformation.mediumNumber || 1; // 中包装数量
            } else if (codeLevel === "3") {
                return this.fixedInformation.packingNumber || 1; // 件包装数量
            }

            return 1;
        },

        // 更新扫描统计
        updateScanStatistics(traceCodeItem) {
            // console.log('更新扫描统计前:', {
            //     scanNumber: this.variableInformation.scanNumber,
            //     needScanNumber: this.variableInformation.needScanNumber,
            //     needScanTime: this.variableInformation.needScanTime,
            //     pkgAmount: traceCodeItem.pkgAmount,
            //     codeLevel: traceCodeItem.codeLevel
            // });

            this.variableInformation.scanNumber += traceCodeItem.pkgAmount;
            this.variableInformation.scanTime += 1;

            // 更新包装统计
            if (traceCodeItem.codeLevel === "1") {
                this.variableInformation.small += 1;
            } else if (traceCodeItem.codeLevel === "2") {
                this.variableInformation.medium += 1;
            } else if (traceCodeItem.codeLevel === "3") {
                this.variableInformation.big += 1;
            }

            // 关键修复：更新需扫描数量（与手动扫码逻辑保持一致）
            this.variableInformation.needScanNumber =
                this.variableInformation.number - this.variableInformation.scanNumber;

            // 关键修复：更新需扫描次数
            // 注意：不能直接调用 codeLevelChange，因为它依赖于 formData.codeLevel
            // 我们需要根据实际的扫描情况重新计算 needScanTime
            this.recalculateNeedScanTime();

            // console.log('更新扫描统计后:', {
            //     scanNumber: this.variableInformation.scanNumber,
            //     needScanNumber: this.variableInformation.needScanNumber,
            //     needScanTime: this.variableInformation.needScanTime
            // });
        },

        // 重新计算需扫描次数（基于实际扫描情况）
        recalculateNeedScanTime() {
            const needScanNumber = this.variableInformation.needScanNumber;

            // 如果已经扫描完成，needScanTime 应该为 0
            if (needScanNumber <= 0) {
                this.variableInformation.needScanTime = 0;
                // console.log('扫描已完成，needScanTime 设为 0');
                return;
            }

            // 根据当前选择的包装级别计算剩余需要扫描的次数
            if (this.formData.codeLevel === "1") {
                this.variableInformation.needScanTime = needScanNumber;
            } else if (this.formData.codeLevel === "2") {
                this.variableInformation.needScanTime =
                    Math.ceil(needScanNumber / this.fixedInformation.mediumNumber);
            } else if (this.formData.codeLevel === "3") {
                this.variableInformation.needScanTime =
                    Math.ceil(needScanNumber / this.fixedInformation.packingNumber);
            }

            // console.log('重新计算 needScanTime:', {
            //     needScanNumber,
            //     codeLevel: this.formData.codeLevel,
            //     needScanTime: this.variableInformation.needScanTime
            // });
        },

        // 数据处理（参考原组件逻辑）
        dataProcessing() {
            // 数据清空
            this.tracingCodeList = [];
            this.tableData = this.defaultInformation.goods ? this.defaultInformation.goods : [];

            const goods = this.tableData[this.readIndex] ? this.tableData[this.readIndex] : {};

            // 追溯码信息
            this.formData = {
                regulatoryCode: "",
                codeLevel: this.getDefaultCodeLevel(),
                pkgAmount: 1,
            };

            // 固定数据
            this.fixedInformation = {
                receiptNumber: this.defaultInformation.allocationCode || '',
                ownerName: goods.ownerName || '',
                clientName: goods.clientName || '',
                productName: goods.productName || '',
                specifications: goods.specifications || '',
                mediumNumber: goods.maxMediumPacking || 0,
                packingNumber: goods.maxLargePacking || 0,
            };

            // 包装追溯码状态数据
            this.packageCodeStatus = {
                smallPackageCode: goods.smallPackageCode || 0,
                mediumPackageCode: goods.mediumPackageCode || 0,
                piecePackageCode: goods.piecePackageCode || 0,
            };

            // 推荐扫码数据
            this.recommendScanData = {
                mediumPackageCount: goods.mediumPackageCount || 0,
                smallPackageCount: goods.smallPackageCount || 0,
                totalScanCount: goods.totalScanCount || 0,
            };

            // 可变数据
            const number = goods.reviewNumber || 0;
            // 修复：needScanNumber应该始终表示商品数量，不是扫码次数
            // totalScanCount如果存在，应该用于needScanTime的计算，而不是needScanNumber

            this.variableInformation = {
                number: number,                    // 商品复核数量（固定值）
                needScanNumber: number,            // 需扫描数量（始终等于商品数量）
                needScanTime: number,              // 需扫描次数（初始值，会在codeLevelChange中重新计算）
                scanNumber: 0,                     // 已扫描数量
                scanTime: 0,                       // 已扫描次数
                big: 0,                           // 已扫描件包装数
                medium: 0,                        // 已扫描中包装数
                small: 0,                         // 已扫描小包装数
            };

            // 初始化时计算需扫描次数（传入true表示初始化，避免误报警告）
            this.codeLevelChange(true);

            // 处理预填充的追溯码（如果当前批号有预填充数据）
            this.processPreFilledTraceCodes();
        },

        // 处理预填充的追溯码
        processPreFilledTraceCodes() {
            const currentBatch = this.tableData[this.readIndex];

            if (currentBatch && currentBatch.preFilledTraceCodes && currentBatch.preFilledTraceCodes.length > 0) {
                // console.log(`处理批号 ${currentBatch.batchNumber} 的预填充追溯码:`, currentBatch.preFilledTraceCodes);

                // 填充预存储的追溯码（不检查完成状态，避免在初始化时触发跳转）
                this.fillCurrentBatchTraceCodes(currentBatch.preFilledTraceCodes, false);

                // 清空预填充数据，避免重复处理
                currentBatch.preFilledTraceCodes = [];

                // console.log(`批号 ${currentBatch.batchNumber} 预填充追溯码处理完成`);
            }
        },

        // 关闭弹窗
        handleClose() {
            this.dialogVisible = false;
            this.resetData();
            this.$emit('on-close');
        },

        // 获取默认的包装类型（优先选择有追溯码的包装类型）
        getDefaultCodeLevel() {
            if (this.packageCodeStatus.smallPackageCode === 1) {
                return "1"; // 小包装
            } else if (this.packageCodeStatus.mediumPackageCode === 1) {
                return "2"; // 中包装
            } else if (this.packageCodeStatus.piecePackageCode === 1) {
                return "3"; // 件包装
            }
            return "1"; // 默认小包装
        },

        // 检查包装类型是否可用（是否有追溯码）
        isCodeLevelAvailable(codeLevel) {
            switch (codeLevel) {
                case "1":
                    return this.packageCodeStatus.smallPackageCode === 1;
                case "2":
                    return this.packageCodeStatus.mediumPackageCode === 1;
                case "3":
                    return this.packageCodeStatus.piecePackageCode === 1;
                default:
                    return false;
            }
        },

        // 验证是否有重复追溯码（参考原组件的重复检查逻辑）
        validateDuplicateCode() {
            const currentCode = this.formData.regulatoryCode;

            // 检查当前批次是否有重复
            for (let item of this.tracingCodeList) {
                if (item.regulatoryCode === currentCode) {
                    return false;
                }
            }

            // 检查已提交批次是否有重复
            for (let item of this.submitData) {
                if (item.regulatoryCode === currentCode) {
                    return false;
                }
            }

            return true;
        },

        // 验证包装数量是否超过总数量（参考原组件的simulationNum验证逻辑）
        validatePackageQuantity() {
            const needScanNumber = this.variableInformation.needScanNumber;
            let packageSize = 1; // 当前包装类型的单包装数量

            // 获取当前包装类型的单包装数量
            if (this.formData.codeLevel === "1") {
                packageSize = 1; // 小包装：1个/包
            } else if (this.formData.codeLevel === "2") {
                packageSize = this.fixedInformation.mediumNumber; // 中包装数量
            } else if (this.formData.codeLevel === "3") {
                packageSize = this.fixedInformation.packingNumber; // 件包装数量
            }

            // 计算扫码后的剩余数量
            const simulationNum = needScanNumber - packageSize;

            // 如果模拟剩余数量小于0，说明这次扫码会超过总数量
            return simulationNum >= 0;
        },

        // 实物无追溯码
        handleNoTracingCode() {
            this.$refs.unlockDialog.open();
        },

        // 解锁确认
        handleUnlockConfirm(userInfo) {
            this.loading = true;
            const params = [{
                unlockType: 2,       // 解锁类型 2 内复核追溯码扫描
                operationName: userInfo.realname,
                unlockAccount: userInfo.staffNum,
                unlockResult: true,
                taskCode: this.defaultInformation.mergeOrderCode,
                productCode: this.defaultInformation.productCode
            }];

            unlockThisTask(params).then(res => {
                this.loading = false;
                const { code, msg } = res;
                if (code === 0) {
                    this.$message.success('解锁成功');
                    this.submitReview('pass');
                } else {
                    this.$message.error(msg || '解锁失败');
                }
            }).catch(() => {
                this.loading = false;
                this.$message.error('解锁失败');
            });
        },



        // 包装规格变化（参考原组件逻辑）
        codeLevelChange(isInitializing = false) {
            if (this.formData.codeLevel === "1") {
                this.variableInformation.needScanTime = this.variableInformation.needScanNumber;
            } else if (this.formData.codeLevel === "2") {
                this.variableInformation.needScanTime =
                    this.variableInformation.needScanNumber / this.fixedInformation.mediumNumber;
            } else if (this.formData.codeLevel === "3") {
                this.variableInformation.needScanTime =
                    this.variableInformation.needScanNumber / this.fixedInformation.packingNumber;
            }

            // 处理小数情况 - 修复浮点数精度问题
            if (this.variableInformation.needScanTime % 1 !== 0) {
                // 使用更精确的小数处理方式
                this.variableInformation.needScanTime = Math.round(this.variableInformation.needScanTime * 10) / 10;
            }

            // 修复验证逻辑：参考原组件的正确实现
            if (isInitializing) return; // 只针对切换包装做相关提示

            // 使用与validatePackageQuantity相同的验证逻辑
            const needScanNumber = this.variableInformation.needScanNumber;
            let packageSize = 1;

            if (this.formData.codeLevel === "1") {
                packageSize = 1;
            } else if (this.formData.codeLevel === "2") {
                packageSize = this.fixedInformation.mediumNumber;
            } else if (this.formData.codeLevel === "3") {
                packageSize = this.fixedInformation.packingNumber;
            }

            // 当包装规格大于剩余需要扫码数量时，给出提示
            if (packageSize > needScanNumber && needScanNumber > 0) {
                this.$message.warning("当前选择包装类型，其合计数量，超过总数量");
            }
        },

        // 扫描追溯码（参考原组件逻辑）
        handleScanCode() {
            if (this.formData.regulatoryCode === "") {
                this.$message.warning("请输入追溯码录入！");
                return;
            }

            if (this.formData.codeLevel === "") {
                this.formData.regulatoryCode = "";
                this.$message.warning("请选择包装规格录入！");
                return;
            }

            // 验证选择的包装类型是否有追溯码
            if (!this.isCodeLevelAvailable(this.formData.codeLevel)) {
                this.formData.regulatoryCode = "";
                this.$message.warning("所选包装类型无追溯码，请选择其他包装类型！");
                return;
            }

            // 验证是否有重复追溯码
            if (!this.validateDuplicateCode()) {
                this.formData.regulatoryCode = "";
                this.$message.warning(this.formData.regulatoryCode + " 此追溯码 该批次或其他批次已录入！");
                return;
            }

            // 验证包装数量是否超过总数量（关键验证逻辑）
            if (!this.validatePackageQuantity()) {
                this.formData.regulatoryCode = "";
                this.$message.warning("当前选择包装类型，其合计数量，超过总数量");
                return;
            }

            // 验证追溯码格式
            if (this.currentBatch.largeCategoryCode != 202) {
                if (this.formData.regulatoryCode.length !== 20 ||
                    !this.formData.regulatoryCode.startsWith("8")) {
                    this.formData.regulatoryCode = "";
                    this.$message.warning("该条码不正确，请检查是否扫码正确！");
                    return;
                }
            }

            // 检查是否重复
            const exists = this.tracingCodeList.find(item =>
                item.regulatoryCode === this.formData.regulatoryCode);
            if (exists) {
                this.$message.warning("该追溯码已存在");
                this.formData.regulatoryCode = "";
                return;
            }

            // 计算包装数量
            let pkgAmount = 1;
            if (this.formData.codeLevel === "1") {
                pkgAmount = 1;
            } else if (this.formData.codeLevel === "2") {
                pkgAmount = this.fixedInformation.mediumNumber;
            } else if (this.formData.codeLevel === "3") {
                pkgAmount = this.fixedInformation.packingNumber;
            }

            // 添加到列表
            this.tracingCodeList.push({
                regulatoryCode: this.formData.regulatoryCode,
                codeLevel: this.formData.codeLevel,
                pkgAmount: pkgAmount,
                batchNumber: this.currentBatch.batchNumber,
                isAutoFilled: false // 标记为手动扫码
            });

            // 更新扫描统计
            this.updateScanStats(pkgAmount);

            // 清空输入
            this.formData.regulatoryCode = "";
            this.$refs.tracingCodeInput.focus();

            // 检查是否完成当前批号
            this.checkBatchComplete();
        },

        // 更新扫描统计
        updateScanStats(pkgAmount) {
            this.variableInformation.scanNumber += pkgAmount;
            this.variableInformation.scanTime += 1;

            // 更新包装统计
            if (this.formData.codeLevel === "1") {
                this.variableInformation.small += 1;
            } else if (this.formData.codeLevel === "2") {
                this.variableInformation.medium += 1;
            } else if (this.formData.codeLevel === "3") {
                this.variableInformation.big += 1;
            }

            // 更新需扫描数量
            this.variableInformation.needScanNumber =
                this.variableInformation.number - this.variableInformation.scanNumber;

            // 更新需扫描次数
            this.codeLevelChange();
        },

        // 检查批号完成
        checkBatchComplete() {
            if (this.variableInformation.needScanTime === 0 ||
                this.variableInformation.needScanTime === "0") {
                setTimeout(() => {
                    // 提交数据保存
                    this.submitData = this.submitData.concat(this.tracingCodeList);

                    // 判断是否还有下一批号
                    this.readIndex += 1;
                    if (this.readIndex === this.tableData.length) {
                        this.readIndex -= 1;
                        // 所有批号完成，提交
                        this.submitReview(true);
                    } else {
                        this.$message.success("该批次追溯码已录入完成，请录入下一批次（标蓝）");
                        // 处理下一批号
                        this.dataProcessing();
                    }
                }, 700);
            }
        },

        // 自动填充场景下的批号完成检查
        checkBatchCompleteForAutoFill() {
            // console.log('检查自动填充批号完成状态:', {
            //     needScanTime: this.variableInformation.needScanTime,
            //     needScanNumber: this.variableInformation.needScanNumber,
            //     readIndex: this.readIndex,
            //     totalBatches: this.tableData.length
            // });

            if (this.variableInformation.needScanTime === 0 ||
                this.variableInformation.needScanTime === "0" ||
                this.variableInformation.needScanNumber <= 0) {

                // console.log('当前批号已完成，准备跳转到下一批号');

                // 提交当前批号数据
                this.submitData = this.submitData.concat(this.tracingCodeList);
                // console.log('已提交当前批号数据到submitData:', this.submitData);

                // 判断是否还有下一批号
                this.readIndex += 1;
                if (this.readIndex >= this.tableData.length) {
                    this.readIndex -= 1;
                    // console.log('所有批号已完成');
                    // 所有批号完成，但不自动提交，等待用户操作
                    return false; // 返回false表示没有更多批号
                } else {
                    // console.log(`跳转到下一批号，索引: ${this.readIndex}`);
                    this.$message.success("该批次追溯码已录入完成，自动跳转到下一批次");

                    // 处理下一批号
                    this.dataProcessing();
                    return true; // 返回true表示还有更多批号
                }
            }

            return false; // 当前批号未完成
        },

        // 删除追溯码
        handleDeleteCode(index) {
            const deletedItem = this.tracingCodeList[index];

            // 更新统计
            this.variableInformation.scanNumber -= deletedItem.pkgAmount;
            this.variableInformation.scanTime -= 1;

            if (deletedItem.codeLevel === "1") {
                this.variableInformation.small -= 1;
            } else if (deletedItem.codeLevel === "2") {
                this.variableInformation.medium -= 1;
            } else if (deletedItem.codeLevel === "3") {
                this.variableInformation.big -= 1;
            }

            // 更新需扫描数量
            this.variableInformation.needScanNumber =
                this.variableInformation.number - this.variableInformation.scanNumber;
            this.codeLevelChange();

            this.tracingCodeList.splice(index, 1);
        },

        // 清空追溯码
        handleClearTracingCodes() {
            this.$confirm('确定要清空所有追溯码吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 重置当前批号的扫描数据
                this.tracingCodeList = [];
                this.variableInformation.scanNumber = 0;
                this.variableInformation.scanTime = 0;
                this.variableInformation.big = 0;
                this.variableInformation.medium = 0;
                this.variableInformation.small = 0;
                this.variableInformation.needScanNumber = this.variableInformation.number;
                this.codeLevelChange();
                this.$message.success('已清空追溯码');
            });
        },

        // 确认提交
        handleConfirm() {
            // 检查是否所有批号都完成
            if (this.readIndex < this.tableData.length - 1 ||
                this.variableInformation.needScanNumber > 0) {
                this.$message.warning('还有批号未完成扫码，请继续扫码');
                return;
            }

            this.submitReview(true);
        },

        // 提交审核
        submitReview(isLast) {
            this.loading = true;
            const parameter = {
                productCode: this.defaultInformation.productCode,
                regulatoryCodes: this.submitData,
                orderCode: this.defaultInformation.orderCode,
                mergeOrderCode: this.defaultInformation.mergeOrderCode,
                maxMediumPacking: this.fixedInformation.mediumNumber || 0  // 新增必需参数：从固定信息中获取最大中包装数量
            };

            review(parameter).then(res => {
                this.loading = false;
                const { code, msg } = res;
                if (code === 0) {
                    if (isLast === true || isLast === 'pass') {
                        // 构建复核完成的商品信息，用于主页面显示
                        const reviewedProductInfo = {
                            result: res.result,
                            productInfo: {
                                productCode: this.defaultInformation.productCode,
                                productName: this.fixedInformation.productName,
                                packingUnit: this.fixedInformation.specifications,
                                barCode: this.currentBatch.barCode || '',
                                batchNumber: this.currentBatch.batchNumber || '',
                                quantity: this.variableInformation.scanNumber || 0,
                                productPic: this.currentBatch.productPic || []
                            }
                        };
                        this.$emit('regulatoryCodeScanBack', reviewedProductInfo);
                        this.handleClose();
                    }
                    this.$message.success(msg || '追溯码保存成功');
                } else {
                    this.$message.error(msg || '追溯码保存失败');
                    // 失败时重置数据
                    this.resetData();
                }
            }).catch(() => {
                this.loading = false;
                this.$message.error('追溯码保存失败');
                this.resetData();
            });
        },

        // 保存并关闭
        async saveAndClose() {
            try {
                this.saveCloseLoading = true;

                // 检测是否存在追溯码数据
                const hasTracingCodeData = this.checkHasTracingCodeData();

                if (!hasTracingCodeData) {
                    // 所有批号都没有追溯码数据，直接关闭弹窗
                    this.handleClose();
                    return;
                }

                // 存在追溯码数据，执行保存操作
                await this.saveTracingCodeData();

                // 保存成功后关闭弹窗
                this.handleClose();

            } catch (error) {
                console.error('保存追溯码失败:', error);
                this.$message.error('保存追溯码失败，请重试');
            } finally {
                this.saveCloseLoading = false;
            }
        },

        // 检测是否存在追溯码数据
        checkHasTracingCodeData() {
            // 检查当前批号是否有追溯码数据
            if (this.tracingCodeList && this.tracingCodeList.length > 0) {
                return true;
            }

            // 检查已提交的数据中是否有追溯码
            if (this.submitData && this.submitData.length > 0) {
                return true;
            }

            // 检查其他批号是否有预填充的追溯码数据
            for (let i = 0; i < this.tableData.length; i++) {
                const batch = this.tableData[i];
                if (batch.preFilledTraceCodes && batch.preFilledTraceCodes.length > 0) {
                    return true;
                }
            }

            return false;
        },

        // 保存追溯码数据
        async saveTracingCodeData() {
            // 收集所有批号的追溯码数据
            const allTracingCodes = this.collectAllTracingCodes();

            if (allTracingCodes.length === 0) {
                return;
            }

            // 构建保存追溯码的参数，参照复核接口的入参格式
            const saveParams = {
                mergeOrderCode: this.defaultInformation.mergeOrderCode,
                productCode: this.defaultInformation.productCode,
                regulatoryCodes: allTracingCodes
            };

            // console.log('保存追溯码参数:', saveParams);

            // 调用保存追溯码接口
            const response = await saveTraceCode(saveParams);

            if (response.code === 0) {
                this.$message.success('追溯码保存成功');
            } else {
                throw new Error(response.msg || '保存失败');
            }
        },

        // 收集所有批号的追溯码数据
        collectAllTracingCodes() {
            let allCodes = [];

            // 添加当前批号的追溯码
            if (this.tracingCodeList && this.tracingCodeList.length > 0) {
                allCodes = allCodes.concat(this.tracingCodeList.map(item => ({
                    regulatoryCode: item.regulatoryCode,
                    codeLevel: item.codeLevel,
                    pkgAmount: item.pkgAmount,
                    batchNumber: item.batchNumber || this.currentBatch.batchNumber
                })));
            }

            // 添加已提交的追溯码数据
            if (this.submitData && this.submitData.length > 0) {
                allCodes = allCodes.concat(this.submitData.map(item => ({
                    regulatoryCode: item.regulatoryCode,
                    codeLevel: item.codeLevel,
                    pkgAmount: item.pkgAmount,
                    batchNumber: item.batchNumber
                })));
            }

            // 添加其他批号的预填充追溯码数据
            for (let i = 0; i < this.tableData.length; i++) {
                const batch = this.tableData[i];
                if (batch.preFilledTraceCodes && batch.preFilledTraceCodes.length > 0) {
                    // 为预填充的追溯码添加批号信息
                    const batchCodes = batch.preFilledTraceCodes.map(code => ({
                        regulatoryCode: code,
                        codeLevel: this.determineCodeLevelForBatch(batch),
                        pkgAmount: this.determinePkgAmountForBatch(batch),
                        batchNumber: batch.batchNumber
                    }));
                    allCodes = allCodes.concat(batchCodes);
                }
            }

            return allCodes;
        },

        // 为指定批号确定包装级别
        determineCodeLevelForBatch(batch) {
            // 包装追溯码状态字段直接在 batch 对象上
            const packageCodeStatus = {
                smallPackageCode: batch.smallPackageCode || 0,
                mediumPackageCode: batch.mediumPackageCode || 0,
                piecePackageCode: batch.piecePackageCode || 0
            };

            if (packageCodeStatus.smallPackageCode === 1) {
                return "1"; // 小包装
            } else if (packageCodeStatus.mediumPackageCode === 1) {
                return "2"; // 中包装
            } else if (packageCodeStatus.piecePackageCode === 1) {
                return "3"; // 件包装
            }

            return "1"; // 默认小包装
        },

        // 为指定批号确定包装数量
        determinePkgAmountForBatch(batch) {
            const codeLevel = this.determineCodeLevelForBatch(batch);

            if (codeLevel === "1") {
                return 1; // 小包装默认1
            } else if (codeLevel === "2") {
                return batch.maxMediumPacking || 1; // 中包装数量
            } else if (codeLevel === "3") {
                return batch.maxLargePacking || 1; // 件包装数量
            }

            return 1;
        },

        // 数据重置
        resetData() {
            this.readIndex = 0;
            this.submitData = [];
            this.dataProcessing();
        }
    }
};
</script>

<style lang="scss" scoped>
// 自定义标题栏样式
.dialog-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0;
    margin: 0;

    .title-text {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        line-height: 1;
        flex: 1;
    }

    .title-buttons {
        display: flex;
        gap: 8px;
        flex-shrink: 0;

        .el-button {
            margin-left: 0;
            margin-right: 0;

            &.el-button--small {
                padding: 6px 12px;
                font-size: 12px;
                border-radius: 3px;
                height: 28px;
                line-height: 1;
            }

            &.el-button--danger {
                background-color: #f56c6c;
                border-color: #f56c6c;
                color: #fff;

                &:hover:not(:disabled) {
                    background-color: #f78989;
                    border-color: #f78989;
                }

                &:active:not(:disabled) {
                    background-color: #dd6161;
                    border-color: #dd6161;
                }

                &:disabled {
                    background-color: #fab6b6;
                    border-color: #fab6b6;
                    color: #fff;
                    cursor: not-allowed;
                }
            }

            &:not(.el-button--danger) {
                background-color: #fff;
                border-color: #dcdfe6;
                color: #606266;

                &:hover:not(:disabled) {
                    color: #409eff;
                    border-color: #c6e2ff;
                    background-color: #ecf5ff;
                }

                &:active:not(:disabled) {
                    color: #3a8ee6;
                    border-color: #3a8ee6;
                }
            }
        }
    }
}

// 确保弹窗标题栏有足够的高度
::v-deep .el-dialog__header {
    padding: 15px 20px;
    border-bottom: 1px solid #e4e7ed;

    .el-dialog__title {
        width: 100%;
    }
}

.scan-info-section {
    display: flex;
    margin-bottom: 20px;
    border: 1px solid #ddd;

    .scan-count {
        flex: 0 0 150px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-right: 1px solid #ddd;
        padding: 11px;

        .label {
            font-size: 14px;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .count {
            font-size: 46px;
            font-weight: bold;
            color: #f56c6c;
        }
    }

    .package-info {
        flex: 1;
        padding: 11px;

        .package-row {
            margin-bottom: 10px;
            font-size: 23px;
            font-weight: bold;

            span {
                margin-right: 20px;
            }

            .package-value {
                color: #f56c6c;
                font-weight: bold;
            }

            .status {
                font-weight: bold;

                &.has-code {
                    color: #67c23a;
                }

                &.no-code {
                    color: #f56c6c;
                }
            }
        }
    }
}

.product-info-section {
    margin-bottom: 20px;
    border: 1px solid #ddd;

    .product-row {
        display: flex;
        padding: 10px 15px;
        border-bottom: 1px solid #eee;

        &:last-child {
            border-bottom: none;
        }

        .label {
            flex: 0 0 80px;
            font-weight: bold;
        }

        .value {
            flex: 1;
        }

        .batch-info {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .batch-item {
                margin-right: 15px;

                .batch-number {
                    color: #f56c6c;
                    font-weight: bold;
                }

                &.active-batch {
                    .batch-number {
                        background-color: #409eff;
                        color: white;
                        padding: 2px 6px;
                        border-radius: 3px;
                    }
                }
            }
        }
    }
}

.scan-recommend-section {
    margin-bottom: 20px;
    display: flex;
    gap: 30px;
    align-items: flex-start;
    padding: 15px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    background-color: #fafafa;

    // 左侧区域 - 扫码推荐数据
    .recommend-data-area {
        flex: 0 0 150px;

        .recommend-title {
            font-size: 14px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 10px;
        }

        .recommend-list {
            .recommend-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
                padding: 4px 8px;
                background-color: #fff;
                border-radius: 3px;
                border: 1px solid #e4e7ed;

                .recommend-label {
                    font-size: 12px;
                    color: #606266;
                }

                .recommend-value {
                    font-size: 14px;
                    font-weight: bold;
                    color: #409eff;
                }
            }

            .total-scan {
                border-top: 1px solid #e4e7ed;
                padding-top: 8px;
                margin-top: 8px;

                .recommend-label {
                    color: #303133;
                    font-weight: bold;
                }

                .recommend-value {
                    color: #e6a23c;
                    font-size: 16px;
                }
            }
        }
    }

    // 中间区域 - 扫码操作区
    .scan-operation-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 15px;

        .package-selection {
            display: flex;
            justify-content: center;

            .el-radio-group {
                .el-radio {
                    margin-right: 20px;
                }
            }
        }

        .scan-input {
            display: flex;
            justify-content: center;

            ::v-deep .el-input {
                width: 300px;
                margin-top: 23px;

                .el-input__inner {
                    height: 69px;
                    line-height: 69px;
                    font-size: 20px;
                }
            }
        }
    }

    // 右侧区域 - 扫码统计
    .scan-statistics-area {
        flex: 0 0 120px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #fff;
        border: 2px solid #409eff;
        border-radius: 6px;
        padding: 15px;
        margin-top: 23px;

        .statistics-label {
            font-size: 12px;
            color: #606266;
            margin-bottom: 8px;
        }

        .statistics-value {
            font-size: 24px;
            font-weight: bold;
            color: #f56c6c;
            text-align: center;
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        flex-direction: column;
        gap: 15px;

        .recommend-data-area {
            flex: none;

            .recommend-list {
                display: flex;
                gap: 10px;

                .recommend-item {
                    margin-bottom: 0;
                }
            }
        }

        .scan-statistics-area {
            flex: none;
            flex-direction: row;
            padding: 10px 15px;

            .statistics-label {
                margin-bottom: 0;
                margin-right: 10px;
            }
        }
    }
}

.tracing-code-list {
    margin-bottom: 20px;

    // 移除表格的overflow设置，避免与外层容器产生双重滚动条
    ::v-deep .el-table {
        font-size: 16px; // 字体变大

        .el-table__body tr {
            height: 10px !important; // 行高变小
        }

        .el-table__cell {
            padding-top: 4px !important;
            padding-bottom: 4px !important;
        }

        // 让表格使用固定高度，内部滚动由Element UI自动处理
        .el-table__body-wrapper {
            overflow-y: auto;
        }
    }
}

.dialog-footer {
    flex-shrink: 0;
    position: sticky;
    bottom: 0;
    background: #fff;
    z-index: 2;
    border-top: 1px solid #e4e7ed;
    padding: 15px 20px;
    text-align: right;
}

// 弹窗整体样式优化
::v-deep .el-dialog {
    // 确保弹窗在小屏幕上不会超出视口
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow-y: auto;
    top: 0 !important;
    right: 0 !important;
    left: auto !important;
    margin-top: 0 !important;
    margin-right: 0 !important;
    position: fixed !important;
    transform: none !important; // 关键：去掉居中

    .el-dialog__body {
        flex: 1 1 auto;
        max-height: calc(100vh - 120px); // 减去头部和底部的高度
        // 移除overflow-y设置，让内部组件自己处理滚动
        padding: 15px 20px;
    }
}

// 响应式设计 - 小屏幕适配
@media (max-height: 768px) {
    .el-dialog {
        // top: 2vh !important;
        max-height: 100vh;

        .el-dialog__body {
            max-height: calc(100vh - 100px);
            padding: 10px 15px;
        }
    }
}

@media (max-height: 600px) {
    .el-dialog {
        // top: 1vh !important;
        max-height: 100vh;

        .el-dialog__body {
            max-height: calc(100vh - 80px);
            padding: 8px 12px;
        }
    }
}
</style>
