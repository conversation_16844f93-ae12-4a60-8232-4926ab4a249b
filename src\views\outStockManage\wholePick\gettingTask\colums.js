
// 采购入库单下传
export function columns (){
  return [
    { field: 'createTime', title: '任务下发时间',sortable:true, width:'120', visible: true },
    { field: 'occupyOrders', title: '占用订单数', sortable:true, width:'120', visible: true},
    { field: 'orderStatusDesc', title: '作业状态', width:'120', visible: true},
    { field: 'erpOrderCode', title: '销售单号', width:'120', visible: true},
    { field: 'orderCode', title: '出库单号', width:'120', visible: true},
    { field: 'taskType', title: '任务类型',width:'120', visible: true},
    { field: 'jobOrderPickingNumber', title: '计划件数', width:'120', visible: true},
    { field: 'orderPickingNumber', title: '实际件数', width:'120', visible: true},
    { field: 'reservoirCode', title: '库区编码', width:'120', visible: true},
    { field: 'soldOut', title: '下架货位', sortable:true, width:'120', visible: true},
    { field: 'replenishmentShelfSpace', title: '补货上架货位', width:'120', visible: true},
    { field: 'productCode', title: '商品编码',width:'120', visible: true},
    { field: 'productName', title: '商品名称', width:'120', visible: true},
    { field: 'batchNumber', title: '批号', width:'120', visible: true},
    { field: 'productUnit', title: '件包装单位', width:'120', visible: true},
    { field: 'channelCode', title: '业务类型', width:'120', visible: true},
    { field: 'tagCode', title: '标签条码', width:'120', visible: true},
    { field: 'batchInspectionCode', title: '批拣单号', width:'120', visible: true},
    { field: 'employeeNumber', title: '拣货员工号', width:'120', visible: true},
    // { field: 'employeeName', title: '拣货员姓名', width:'120px', visible: true},
  ]
}

export function dialogColumus1(){
  return [
    {field: 'scanStatusDesc', title: '扫描状态', width: '100px'},
    {field: 'supervisionName', title: '监管类型', width: '100px'},
    {field: 'erpOrderCode', title: '单据编号', width: '100px'},
    {field: 'clientName', title: '客户名称', width: '100px'},
    {field: 'ownerName', title: '业主名称', width: '100px'},
    {field: 'productName', title: '商品名称', width: '100px'},
    {field: 'specifications', title: '规格', width: '100px'},
    {field: 'manufacturer', title: '生产厂家', width: '100px'},
    {field: 'packageSpecification', title: '件包装数', width: '100px'},
    {field: 'wholeNumber', title: '需扫件数', width: '100px'},
    {field: 'completeNumber', title: '已扫件数', width: '100px'},
    {field: 'workingAreaBegin', title: '发货区起始号', width: '100px'},
    {field: 'workingAreaEnd', title: '发货区终止号', width: '100px'},
    {field: 'batchInspectionCode', title: '任务单号', width: '100px'},
  ]
}

export function dialogColumus2(){
  return [
    {field: 'regulatoryCode', title: '追溯码/物流码', width: '100px'},
    {field: 'packageLevel', title: '包装级别', width: '100px'},
    {field: 'codeTypeDesc', title: '码类型', width: '100px'},
    {field: 'createTime', title: '扫码时间', width: '100px'},
  ]
}